
Content is user-generated and unverified.
1
# Python cache and bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
ENV/
env/
.env
.env.local
.env.development
.env.test
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
.logs/

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/

# Documentation
docs/_build/

# Git
.git/
.gitignore

# Docker files (except the ones we're using)
docker-compose*.yml

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Output directories (will be created in container)
output/
temp/
tmp/

# Node modules (if any)
node_modules/

# Jupyter notebooks
.ipynb_checkpoints/

# Project specific development files
test_workflow.py
ComfyUI_temp_*
ComfyUI_0*

# Local development scripts (not needed in container)
prepare_custom_nodes.py
custom_nodes_config.json

# RunPod template (generated file)
runpod-template.json

# EXCLUDE large downloads that will be done in RunPod
# We want these config files but not the actual downloads
!comfy-requirements.txt
!custom-nodes-repos.txt  
!model-downloads.txt
!setup-environment.sh
!startup.sh

# OLD scripts we're not using anymore
install-nodes.sh
download-models.sh
