#!/bin/bash
# Setup environment script - Runs on first boot in RunPod
# Downloads ComfyUI, models, and custom nodes with fast RunPod network

set -e

SETUP_MARKER="/workspace/.setup_complete"
COMFYUI_DIR="/workspace/ComfyUI"

echo "🚀 Setting up ComfyUI environment..."

# Check if setup already completed
if [ -f "$SETUP_MARKER" ]; then
    echo "✅ Environment already set up, skipping..."
    return 0
fi

# Function to download with progress
download_with_progress() {
    local url="$1"
    local dest="$2"
    local desc="$3"
    
    echo "📥 Downloading: $desc"
    echo "   URL: $url"
    echo "   Destination: $dest"
    
    mkdir -p "$(dirname "$dest")"
    
    if wget -O "$dest.tmp" "$url" --progress=bar:force:noscroll --timeout=30 --tries=3; then
        mv "$dest.tmp" "$dest"
        size=$(stat -c%s "$dest" 2>/dev/null || echo "unknown")
        echo "✅ Downloaded: $(basename "$dest") ($size bytes)"
        return 0
    else
        echo "❌ Failed to download: $desc"
        rm -f "$dest.tmp"
        return 1
    fi
}

# Step 0: Install PyTorch first (with fast RunPod network!)
echo "📦 Installing PyTorch 2.8.0+cu129 (this will be much faster on RunPod)..."
pip install torch==2.8.0+cu129 torchvision --extra-index-url https://download.pytorch.org/whl/cu129

# Install comfy-cli now that we have PyTorch
echo "📦 Installing comfy-cli..."
pip install comfy-cli

# Step 1: Clone ComfyUI if not exists
if [ ! -d "$COMFYUI_DIR/.git" ]; then
    echo "📦 Cloning ComfyUI..."
    rm -rf "$COMFYUI_DIR"
    git clone https://github.com/comfyanonymous/ComfyUI.git "$COMFYUI_DIR"
    echo "✅ ComfyUI cloned"
else
    echo "✅ ComfyUI already exists"
fi

cd "$COMFYUI_DIR"

# Step 2: Install ComfyUI requirements
echo "📦 Installing ComfyUI requirements..."
pip install -r requirements.txt

# Step 3: Download models
echo "📥 Downloading FLUX models (this may take 5-10 minutes)..."

# Create model directories
mkdir -p models/clip models/diffusion_models models/vae models/clip_vision models/style_models

# Download each model
while IFS='|' read -r url dest desc; do
    if [[ "$url" =~ ^https:// ]] && [ ! -z "$dest" ] && [ ! -z "$desc" ]; then
        if [ ! -f "$dest" ] || [ ! -s "$dest" ]; then
            download_with_progress "$url" "$dest" "$desc" || echo "⚠️  Continuing..."
        else
            echo "✅ Already exists: $(basename "$dest")"
        fi
    fi
done < /workspace/model-downloads.txt

# Step 4: Install custom nodes from repositories
echo "📦 Installing custom nodes from repositories..."
cd custom_nodes

while read -r repo; do
    if [[ "$repo" =~ ^https://github.com ]] && [ ! -z "$repo" ]; then
        repo_name=$(basename "$repo" .git)
        echo "📦 Installing: $repo_name"
        
        if [ ! -d "$repo_name" ]; then
            git clone "$repo" "$repo_name" || echo "⚠️  Failed to clone $repo_name"
        else
            echo "✅ Already exists: $repo_name"
        fi
        
        # Install requirements if they exist
        if [ -f "$repo_name/requirements.txt" ]; then
            echo "   📋 Installing requirements for $repo_name"
            pip install -r "$repo_name/requirements.txt" || echo "⚠️  Failed to install requirements for $repo_name"
        fi
    fi
done < /workspace/custom-nodes-repos.txt

# Step 5: Install registry nodes via comfy-cli
echo "📦 Installing ComfyUI registry nodes..."

# Set non-interactive mode
export COMFY_CLI_NON_INTERACTIVE=1

# Install each registry node
while read -r node; do
    if [ ! -z "$node" ] && [[ ! "$node" =~ ^# ]]; then
        echo "📦 Installing registry node: $node"
        # Try multiple approaches for comfy-cli
        comfy node install "$node" --skip-prompt 2>/dev/null || \
        comfy node install "$node" --yes 2>/dev/null || \
        echo "⚠️  Failed to install $node via comfy-cli"
    fi
done < /workspace/comfy-requirements.txt

# Step 6: Install ComfyUI-Manager manually
if [ ! -d "ComfyUI-Manager" ]; then
    echo "📦 Installing ComfyUI-Manager..."
    git clone https://github.com/ltdrdata/ComfyUI-Manager.git
    if [ -f "ComfyUI-Manager/requirements.txt" ]; then
        pip install -r ComfyUI-Manager/requirements.txt || echo "⚠️  Failed to install ComfyUI-Manager requirements"
    fi
fi

cd /workspace

# Step 7: Verify setup
echo "🔍 Verifying setup..."

critical_models=(
    "ComfyUI/models/clip/t5xxl_fp16.safetensors"
    "ComfyUI/models/clip/clip_l.safetensors"
    "ComfyUI/models/diffusion_models/unet_fp16.safetensors"
    "ComfyUI/models/vae/ae.safetensors"
)

missing_models=0
for model in "${critical_models[@]}"; do
    if [ -f "$model" ] && [ -s "$model" ]; then
        echo "✅ $model"
    else
        echo "❌ Missing: $model"
        missing_models=$((missing_models + 1))
    fi
done

# Mark setup as complete
touch "$SETUP_MARKER"

echo ""
echo "📊 Setup Summary:"
echo "=================="
echo "✅ ComfyUI: Installed"
echo "✅ Custom nodes: $(ls ComfyUI/custom_nodes | wc -l) installed"
echo "✅ Models: $((4 - missing_models))/4 critical models present"

if [ $missing_models -eq 0 ]; then
    echo "🎉 Environment setup completed successfully!"
    return 0
else
    echo "⚠️  Setup completed with $missing_models missing models"
    echo "   ComfyUI may not work properly without these models"
    return 1
fi