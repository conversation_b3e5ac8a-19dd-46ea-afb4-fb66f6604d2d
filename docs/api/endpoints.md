# API Endpoints Reference

## Overview

This document provides complete specifications for all available API endpoints in the ComfyUI Inpainting Backend.

## Base URL

```
http://localhost:8000
```

## Endpoints Summary

| Method | Endpoint                          | Description                        | Authentication |
| ------ | --------------------------------- | ---------------------------------- | -------------- |
| GET    | `/api/health`                     | Health check                       | None           |
| POST   | `/api/upload`                     | Upload images                      | None           |
| POST   | `/api/inpaint/run`                | Start workflow with existing files | None           |
| POST   | `/api/inpaint/run-with-files`     | Upload and start workflow          | None           |
| GET    | `/api/inpaint/result/{prompt_id}` | Download result image              | None           |
| WS     | `/ws/progress/{prompt_id}`        | Real-time progress updates         | None           |

---

## Health Check

### GET `/api/health`

Health check endpoint to verify the API is running.

#### Request

- **Method**: GET
- **URL**: `/api/health`
- **Headers**: None required
- **Body**: None

#### Response

**Success (200 OK)**

```json
{
  "status": "ok"
}
```

#### Example

```bash
curl -X GET "http://localhost:8000/api/health"
```

---

## File Upload

### POST `/api/upload`

Upload images for curtain fabric workflow. Accepts three image files and saves them to the input directory with standardized names.

#### Request

- **Method**: POST
- **URL**: `/api/upload`
- **Content-Type**: `multipart/form-data`

#### Parameters

| Parameter           | Type | Required | Description                                         |
| ------------------- | ---- | -------- | --------------------------------------------------- |
| `fabric_swatch`     | File | Yes      | Fabric texture/pattern image (PNG, JPG, JPEG, WebP) |
| `scene_destination` | File | Yes      | Room/window scene image (PNG, JPG, JPEG, WebP)      |
| `curtain_mask`      | File | Yes      | Curtain mask image (PNG, JPG, JPEG, WebP)           |

#### Response

**Success (200 OK)**

```json
{
  "message": "Files uploaded successfully",
  "session_id": "a1b2c3d4",
  "fabric_swatch": "fabric_swatch_a1b2c3d4.png",
  "scene_destination": "scene_destination_a1b2c3d4.png",
  "curtain_mask": "curtain_mask_a1b2c3d4.png"
}
```

#### Error Responses

**Bad Request (400)**

```json
{
  "detail": "fabric_swatch must be an image file (PNG, JPG, JPEG, or WebP)"
}
```

**Internal Server Error (500)**

```json
{
  "detail": "Failed to save uploaded files: [error details]"
}
```

#### Example

```javascript
const formData = new FormData();
formData.append("fabric_swatch", fabricFile);
formData.append("scene_destination", sceneFile);
formData.append("curtain_mask", maskFile);

const response = await fetch("/api/upload", {
  method: "POST",
  body: formData,
});
```

---

## Start Workflow

### POST `/api/inpaint/run`

Start a curtain fabric inpainting workflow using previously uploaded files.

#### Request

- **Method**: POST
- **URL**: `/api/inpaint/run`
- **Content-Type**: `application/json`

#### Request Body Schema

```json
{
  "fabric_swatch": "string",
  "scene_destination": "string",
  "curtain_mask": "string",
  "comfy_server": "string (optional)"
}
```

#### Parameters

| Field               | Type   | Required | Description                              | Example                   |
| ------------------- | ------ | -------- | ---------------------------------------- | ------------------------- |
| `fabric_swatch`     | string | Yes      | Filename in input/ for fabric swatch     | `"fabric_swatch.png"`     |
| `scene_destination` | string | Yes      | Filename in input/ for destination scene | `"scene_destination.png"` |
| `curtain_mask`      | string | Yes      | Filename in input/ for curtain mask      | `"curtain_mask.png"`      |
| `comfy_server`      | string | No       | Override ComfyUI server address          | `"127.0.0.1:8188"`        |

#### Response

**Success (200 OK)**

```json
{
  "prompt_id": "abc123-def456-ghi789",
  "progress_ws": "/ws/progress/abc123-def456-ghi789",
  "result_url": "/api/inpaint/result/abc123-def456-ghi789"
}
```

#### Error Responses

**Bad Request (400)**

```json
{
  "detail": "Input file not found: fabric_swatch.png"
}
```

**Internal Server Error (500)**

```json
{
  "detail": "Failed to patch workflow: [error details]"
}
```

**Service Unavailable (503)**

```json
{
  "detail": "Failed to upload inputs to ComfyUI: [error details]"
}
```

#### Example Request

```json
{
  "fabric_swatch": "fabric_swatch_a1b2c3d4.png",
  "scene_destination": "scene_destination_a1b2c3d4.png",
  "curtain_mask": "curtain_mask_a1b2c3d4.png",
  "comfy_server": "127.0.0.1:8188"
}
```

#### Example

```javascript
const response = await fetch("/api/inpaint/run", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    fabric_swatch: "fabric_swatch_a1b2c3d4.png",
    scene_destination: "scene_destination_a1b2c3d4.png",
    curtain_mask: "curtain_mask_a1b2c3d4.png",
  }),
});
```

---

## Upload and Start Workflow

### POST `/api/inpaint/run-with-files`

Upload images and start curtain fabric workflow in one step. This endpoint combines file upload and workflow execution.

#### Request

- **Method**: POST
- **URL**: `/api/inpaint/run-with-files`
- **Content-Type**: `multipart/form-data`

#### Parameters

| Parameter           | Type   | Required | Description                     |
| ------------------- | ------ | -------- | ------------------------------- |
| `fabric_swatch`     | File   | Yes      | Fabric texture/pattern image    |
| `scene_destination` | File   | Yes      | Room/window scene image         |
| `curtain_mask`      | File   | Yes      | Curtain mask image              |
| `comfy_server`      | string | No       | Override ComfyUI server address |

#### Response

**Success (200 OK)**

```json
{
  "prompt_id": "abc123-def456-ghi789",
  "progress_ws": "/ws/progress/abc123-def456-ghi789",
  "result_url": "/api/inpaint/result/abc123-def456-ghi789"
}
```

#### Error Responses

Same as individual upload and run endpoints.

#### Example

```javascript
const formData = new FormData();
formData.append("fabric_swatch", fabricFile);
formData.append("scene_destination", sceneFile);
formData.append("curtain_mask", maskFile);
formData.append("comfy_server", "127.0.0.1:8188");

const response = await fetch("/api/inpaint/run-with-files", {
  method: "POST",
  body: formData,
});
```

---

## Download Result

### GET `/api/inpaint/result/{prompt_id}`

Download the generated inpainting result. Returns the PNG image file generated by the inpainting workflow.

#### Request

- **Method**: GET
- **URL**: `/api/inpaint/result/{prompt_id}`
- **Headers**: None required

#### Path Parameters

| Parameter   | Type   | Required | Description                            |
| ----------- | ------ | -------- | -------------------------------------- |
| `prompt_id` | string | Yes      | Unique ID returned from workflow start |

#### Response

**Success (200 OK)**

- **Content-Type**: `image/png`
- **Body**: Binary image data

#### Error Responses

**Not Found (404)**

```json
{
  "detail": "No outputs found for this prompt"
}
```

**Bad Gateway (502)**

```json
{
  "detail": "Failed to fetch result: [error details]"
}
```

#### Example

```javascript
// Download result image
const response = await fetch(`/api/inpaint/result/${promptId}`);
if (response.ok) {
  const blob = await response.blob();
  const imageUrl = URL.createObjectURL(blob);
  // Use imageUrl to display or download the image
}
```

```bash
# Download with curl
curl -X GET "http://localhost:8000/api/inpaint/result/abc123-def456-ghi789" \
  --output result.png
```

---

## WebSocket Progress Tracking

### WS `/ws/progress/{prompt_id}`

Real-time progress updates for workflow execution via WebSocket connection.

#### Connection

- **Protocol**: WebSocket
- **URL**: `/ws/progress/{prompt_id}`

#### Path Parameters

| Parameter   | Type   | Required | Description                            |
| ----------- | ------ | -------- | -------------------------------------- |
| `prompt_id` | string | Yes      | Unique ID returned from workflow start |

#### Message Types

The WebSocket sends JSON messages with different types:

#### Progress Message

```json
{
  "type": "progress",
  "data": {
    "value": 5,
    "max": 20,
    "prompt_id": "abc123-def456-ghi789",
    "node": "25"
  }
}
```

#### Executing Message

```json
{
  "type": "executing",
  "data": {
    "node": "25",
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

#### Completion Message

```json
{
  "type": "executing",
  "data": {
    "node": null,
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

#### Error Message

```json
{
  "type": "error",
  "message": "Connection failed or processing error"
}
```

#### Example

```javascript
const ws = new WebSocket(`ws://localhost:8000/ws/progress/${promptId}`);

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);

  switch (data.type) {
    case "progress":
      const percentage = (data.data.value / data.data.max) * 100;
      console.log(`Progress: ${percentage.toFixed(1)}%`);
      break;

    case "executing":
      if (data.data.node === null) {
        console.log("Workflow completed!");
        ws.close();
      } else {
        console.log(`Executing node: ${data.data.node}`);
      }
      break;

    case "error":
      console.error("Error:", data.message);
      break;
  }
};

ws.onerror = (error) => {
  console.error("WebSocket error:", error);
};

ws.onclose = () => {
  console.log("Progress stream disconnected");
};
```

---

## Error Handling

### HTTP Status Codes

| Code | Description           | Common Causes                                         |
| ---- | --------------------- | ----------------------------------------------------- |
| 200  | Success               | Request completed successfully                        |
| 400  | Bad Request           | Invalid parameters, missing files, invalid filenames  |
| 404  | Not Found             | Prompt ID not found, no results available             |
| 500  | Internal Server Error | Workflow patching failed, server configuration issues |
| 502  | Bad Gateway           | Failed to fetch results from ComfyUI                  |
| 503  | Service Unavailable   | ComfyUI server unavailable, upload failures           |

### Error Response Format

All error responses follow this structure:

```json
{
  "detail": "Human-readable error message with specific details"
}
```

### Common Error Scenarios

#### File Not Found

```json
{
  "detail": "Input file not found: fabric_swatch.png"
}
```

#### Invalid File Type

```json
{
  "detail": "fabric_swatch must be an image file (PNG, JPG, JPEG, or WebP)"
}
```

#### ComfyUI Connection Failed

```json
{
  "detail": "Failed to upload inputs to ComfyUI: Connection refused"
}
```

#### No Results Available

```json
{
  "detail": "No outputs found for this prompt"
}
```

---

## Rate Limiting

Currently, there are no rate limits implemented. However, consider implementing the following in production:

- **File Upload**: Limit file size to 10MB per file
- **Concurrent Workflows**: Limit to 5 concurrent workflows per client
- **WebSocket Connections**: Limit to 10 connections per client

---

## Security Considerations

### File Upload Security

- Only image files are accepted (PNG, JPG, JPEG, WebP)
- Files are validated for proper format
- Filenames are sanitized to prevent directory traversal
- Session-based naming prevents file conflicts

### Input Validation

- All filenames are validated to contain only base names (no paths)
- File existence is verified before processing
- ComfyUI server address validation (if provided)

### CORS Configuration

- Currently configured to allow all origins (`*`)
- In production, restrict to specific domains
- Credentials are allowed for authenticated requests
