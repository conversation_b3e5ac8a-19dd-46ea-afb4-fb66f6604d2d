# WebSocket Real-time Progress Tracking Guide

## Overview

The ComfyUI Inpainting Backend provides real-time progress tracking through WebSocket connections. This allows frontend applications to monitor workflow execution, display progress updates, and handle completion or error events as they occur.

## WebSocket Endpoint

```
ws://localhost:8000/ws/progress/{prompt_id}
```

### Parameters

| Parameter   | Type   | Required | Description                                                    |
| ----------- | ------ | -------- | -------------------------------------------------------------- |
| `prompt_id` | string | Yes      | Unique workflow ID returned from `/api/inpaint/run-with-files` |

## Connection Lifecycle

### 1. Connection Establishment

```javascript
const promptId = "abc123-def456-ghi789";
const ws = new WebSocket(`ws://localhost:8000/ws/progress/${promptId}`);

ws.onopen = () => {
  console.log("Progress WebSocket connected");
};
```

### 2. Message Handling

```javascript
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  handleProgressEvent(data);
};
```

### 3. Connection Cleanup

```javascript
ws.onclose = (event) => {
  if (event.wasClean) {
    console.log("Progress stream closed cleanly");
  } else {
    console.log("Connection lost unexpectedly");
  }
};

ws.onerror = (error) => {
  console.error("WebSocket error:", error);
};
```

## Message Types

### Progress Messages

Sent during K-sampler steps and other progress-reporting nodes.

```json
{
  "type": "progress",
  "data": {
    "value": 5,
    "max": 20,
    "prompt_id": "abc123-def456-ghi789",
    "node": "25"
  }
}
```

**Fields:**

- `value`: Current progress step (integer)
- `max`: Total steps for this operation (integer)
- `prompt_id`: Workflow identifier
- `node`: ComfyUI node ID being executed

**Progress Calculation:**

```javascript
const percentage = (data.value / data.max) * 100;
```

### Executing Messages

Sent when workflow nodes start or finish execution.

```json
{
  "type": "executing",
  "data": {
    "node": "25",
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

**Node Execution:**

- `node`: String ID of the executing node
- When `node` is `null`, the workflow has completed

### Completion Detection

```json
{
  "type": "executing",
  "data": {
    "node": null,
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

This message indicates workflow completion. The WebSocket connection should be closed after receiving this message.

### Execution Cached Messages

Sent when nodes are skipped due to caching.

```json
{
  "type": "execution_cached",
  "data": {
    "nodes": ["18", "294"],
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

### Executed Messages

Sent when nodes complete execution.

```json
{
  "type": "executed",
  "data": {
    "node": "25",
    "prompt_id": "abc123-def456-ghi789",
    "output": {
      "images": [
        {
          "filename": "ComfyUI_00001_.png",
          "subfolder": "",
          "type": "output"
        }
      ]
    }
  }
}
```

### Error Messages

Sent when errors occur during processing.

```json
{
  "type": "error",
  "message": "Connection failed or processing error"
}
```

## Implementation Examples

### Basic Progress Tracker

```javascript
class ProgressTracker {
  constructor(promptId, onProgress, onComplete, onError) {
    this.promptId = promptId;
    this.onProgress = onProgress;
    this.onComplete = onComplete;
    this.onError = onError;
    this.ws = null;
  }

  connect() {
    const wsUrl = `ws://localhost:8000/ws/progress/${this.promptId}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log("Progress tracking started");
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error("Failed to parse message:", error);
      }
    };

    this.ws.onerror = (error) => {
      this.onError("WebSocket connection error");
    };

    this.ws.onclose = (event) => {
      if (!event.wasClean) {
        this.onError("Connection lost unexpectedly");
      }
    };
  }

  handleMessage(data) {
    switch (data.type) {
      case "progress":
        if (data.data) {
          const percentage = (data.data.value / data.data.max) * 100;
          this.onProgress(Math.round(percentage), data.data.node);
        }
        break;

      case "executing":
        if (data.data && data.data.node === null) {
          this.onComplete();
          this.disconnect();
        } else if (data.data) {
          console.log(`Executing node: ${data.data.node}`);
        }
        break;

      case "error":
        this.onError(data.message || "Unknown error occurred");
        break;

      default:
        console.log("Received message:", data);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

### Advanced Progress Tracker with Reconnection

```javascript
class RobustProgressTracker {
  constructor(promptId, callbacks) {
    this.promptId = promptId;
    this.callbacks = callbacks;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.isCompleted = false;
  }

  connect() {
    if (this.isCompleted) return;

    const wsUrl = `ws://localhost:8000/ws/progress/${this.promptId}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log("Progress WebSocket connected");
      this.reconnectAttempts = 0;
      this.callbacks.onConnect?.();
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error("Failed to parse progress message:", error);
      }
    };

    this.ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };

    this.ws.onclose = (event) => {
      if (this.isCompleted || event.wasClean) {
        console.log("Progress stream closed cleanly");
        return;
      }

      // Attempt reconnection
      this.attemptReconnection();
    };
  }

  handleMessage(data) {
    switch (data.type) {
      case "progress":
        if (data.data) {
          const percentage = (data.data.value / data.data.max) * 100;
          this.callbacks.onProgress?.(Math.round(percentage), {
            node: data.data.node,
            step: data.data.value,
            totalSteps: data.data.max,
          });
        }
        break;

      case "executing":
        if (data.data && data.data.node === null) {
          this.isCompleted = true;
          this.callbacks.onComplete?.();
          this.disconnect();
        } else if (data.data) {
          this.callbacks.onNodeStart?.(data.data.node);
        }
        break;

      case "execution_cached":
        if (data.data) {
          this.callbacks.onNodesCached?.(data.data.nodes);
        }
        break;

      case "executed":
        if (data.data) {
          this.callbacks.onNodeComplete?.(data.data.node, data.data.output);
        }
        break;

      case "error":
        this.callbacks.onError?.(data.message || "Unknown error occurred");
        break;

      default:
        console.log("Unhandled message type:", data.type);
    }
  }

  attemptReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.callbacks.onError?.(
        "Failed to maintain connection after multiple attempts"
      );
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(
      `Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`
    );

    setTimeout(() => {
      this.connect();
    }, delay);
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

### Usage Examples

#### React Hook for Progress Tracking

```typescript
import { useEffect, useRef, useState } from "react";

interface ProgressState {
  percentage: number;
  currentNode: string | null;
  isConnected: boolean;
  isCompleted: boolean;
  error: string | null;
}

export function useProgressTracking(promptId: string | null) {
  const [state, setState] = useState<ProgressState>({
    percentage: 0,
    currentNode: null,
    isConnected: false,
    isCompleted: false,
    error: null,
  });

  const trackerRef = useRef<RobustProgressTracker | null>(null);

  useEffect(() => {
    if (!promptId) return;

    const tracker = new RobustProgressTracker(promptId, {
      onConnect: () => {
        setState((prev) => ({ ...prev, isConnected: true, error: null }));
      },
      onProgress: (percentage, details) => {
        setState((prev) => ({
          ...prev,
          percentage,
          currentNode: details.node,
        }));
      },
      onComplete: () => {
        setState((prev) => ({
          ...prev,
          isCompleted: true,
          percentage: 100,
          isConnected: false,
        }));
      },
      onError: (error) => {
        setState((prev) => ({
          ...prev,
          error,
          isConnected: false,
        }));
      },
    });

    trackerRef.current = tracker;
    tracker.connect();

    return () => {
      tracker.disconnect();
    };
  }, [promptId]);

  const reconnect = () => {
    if (trackerRef.current && promptId) {
      trackerRef.current.connect();
    }
  };

  return { ...state, reconnect };
}
```

#### Vue.js Composition API

```typescript
import { ref, onUnmounted, watch } from "vue";

export function useProgressTracking(promptId: Ref<string | null>) {
  const percentage = ref(0);
  const currentNode = ref<string | null>(null);
  const isConnected = ref(false);
  const isCompleted = ref(false);
  const error = ref<string | null>(null);

  let tracker: RobustProgressTracker | null = null;

  const connect = (id: string) => {
    disconnect();

    tracker = new RobustProgressTracker(id, {
      onConnect: () => {
        isConnected.value = true;
        error.value = null;
      },
      onProgress: (pct, details) => {
        percentage.value = pct;
        currentNode.value = details.node;
      },
      onComplete: () => {
        isCompleted.value = true;
        percentage.value = 100;
        isConnected.value = false;
      },
      onError: (err) => {
        error.value = err;
        isConnected.value = false;
      },
    });

    tracker.connect();
  };

  const disconnect = () => {
    if (tracker) {
      tracker.disconnect();
      tracker = null;
    }
  };

  watch(
    promptId,
    (newId) => {
      if (newId) {
        connect(newId);
      } else {
        disconnect();
      }
    },
    { immediate: true }
  );

  onUnmounted(() => {
    disconnect();
  });

  return {
    percentage: readonly(percentage),
    currentNode: readonly(currentNode),
    isConnected: readonly(isConnected),
    isCompleted: readonly(isCompleted),
    error: readonly(error),
    reconnect: () => promptId.value && connect(promptId.value),
  };
}
```

## Best Practices

### 1. Connection Management

```javascript
class ConnectionManager {
  constructor() {
    this.activeConnections = new Map();
    this.connectionTimeout = 30000; // 30 seconds
  }

  createConnection(promptId, callbacks) {
    // Close existing connection for this prompt
    this.closeConnection(promptId);

    const tracker = new RobustProgressTracker(promptId, {
      ...callbacks,
      onComplete: () => {
        callbacks.onComplete?.();
        this.closeConnection(promptId);
      },
      onError: (error) => {
        callbacks.onError?.(error);
        this.closeConnection(promptId);
      },
    });

    // Set connection timeout
    const timeoutId = setTimeout(() => {
      tracker.disconnect();
      callbacks.onError?.("Connection timeout");
      this.closeConnection(promptId);
    }, this.connectionTimeout);

    this.activeConnections.set(promptId, {
      tracker,
      timeoutId,
    });

    tracker.connect();
    return tracker;
  }

  closeConnection(promptId) {
    const connection = this.activeConnections.get(promptId);
    if (connection) {
      connection.tracker.disconnect();
      clearTimeout(connection.timeoutId);
      this.activeConnections.delete(promptId);
    }
  }

  closeAllConnections() {
    for (const [promptId] of this.activeConnections) {
      this.closeConnection(promptId);
    }
  }
}
```

### 2. Fallback to HTTP Polling

```javascript
class ProgressTrackingWithFallback {
  constructor(promptId, callbacks) {
    this.promptId = promptId;
    this.callbacks = callbacks;
    this.wsTracker = null;
    this.pollInterval = null;
    this.useWebSocket = true;
  }

  start() {
    if (this.useWebSocket) {
      this.startWebSocketTracking();
    } else {
      this.startPolling();
    }
  }

  startWebSocketTracking() {
    this.wsTracker = new RobustProgressTracker(this.promptId, {
      ...this.callbacks,
      onError: (error) => {
        console.warn("WebSocket failed, falling back to polling:", error);
        this.useWebSocket = false;
        this.startPolling();
        this.callbacks.onError?.(error);
      },
    });

    this.wsTracker.connect();
  }

  async startPolling() {
    const pollForCompletion = async () => {
      try {
        const response = await fetch(`/api/inpaint/result/${this.promptId}`);

        if (response.ok) {
          // Workflow completed
          this.callbacks.onComplete?.();
          this.stop();
        } else if (response.status === 404) {
          // Still processing, continue polling
          this.pollInterval = setTimeout(pollForCompletion, 2000);
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        this.callbacks.onError?.(`Polling failed: ${error.message}`);
        this.stop();
      }
    };

    // Start polling
    this.pollInterval = setTimeout(pollForCompletion, 2000);
  }

  stop() {
    if (this.wsTracker) {
      this.wsTracker.disconnect();
      this.wsTracker = null;
    }

    if (this.pollInterval) {
      clearTimeout(this.pollInterval);
      this.pollInterval = null;
    }
  }
}
```

### 3. Progress Aggregation

```javascript
class ProgressAggregator {
  constructor() {
    this.nodeProgress = new Map();
    this.nodeWeights = new Map();
    this.totalWeight = 0;
  }

  updateNodeProgress(nodeId, progress, weight = 1) {
    if (!this.nodeWeights.has(nodeId)) {
      this.nodeWeights.set(nodeId, weight);
      this.totalWeight += weight;
    }

    this.nodeProgress.set(nodeId, progress);
  }

  getOverallProgress() {
    if (this.totalWeight === 0) return 0;

    let weightedSum = 0;
    for (const [nodeId, progress] of this.nodeProgress) {
      const weight = this.nodeWeights.get(nodeId) || 1;
      weightedSum += progress * weight;
    }

    return Math.round(weightedSum / this.totalWeight);
  }

  reset() {
    this.nodeProgress.clear();
    this.nodeWeights.clear();
    this.totalWeight = 0;
  }
}
```

## Troubleshooting

### Common Issues

#### 1. Connection Refused

**Problem**: WebSocket connection fails immediately

**Causes**:

- Backend server not running
- Incorrect WebSocket URL
- Firewall blocking WebSocket connections

**Solutions**:

```javascript
// Check if server is running
fetch("/api/health")
  .then((response) => {
    if (response.ok) {
      console.log("Server is running");
    } else {
      console.error("Server health check failed");
    }
  })
  .catch((error) => {
    console.error("Cannot reach server:", error);
  });

// Use correct WebSocket URL format
const wsUrl = `ws://${window.location.host}/ws/progress/${promptId}`;
```

#### 2. Connection Drops Frequently

**Problem**: WebSocket disconnects unexpectedly

**Causes**:

- Network instability
- Server restarts
- Proxy/load balancer timeouts

**Solutions**:

```javascript
// Implement heartbeat/ping
class HeartbeatTracker extends RobustProgressTracker {
  constructor(promptId, callbacks) {
    super(promptId, callbacks);
    this.heartbeatInterval = null;
    this.lastHeartbeat = Date.now();
  }

  connect() {
    super.connect();
    this.startHeartbeat();
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // Send ping (if server supports it)
        this.ws.send(JSON.stringify({ type: "ping" }));

        // Check if we've received recent messages
        if (Date.now() - this.lastHeartbeat > 30000) {
          console.warn("No heartbeat received, reconnecting...");
          this.attemptReconnection();
        }
      }
    }, 10000);
  }

  handleMessage(data) {
    this.lastHeartbeat = Date.now();
    super.handleMessage(data);
  }

  disconnect() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    super.disconnect();
  }
}
```

#### 3. No Progress Updates

**Problem**: Connected but no progress messages received

**Causes**:

- Invalid prompt_id
- Workflow already completed
- ComfyUI not sending progress events

**Solutions**:

```javascript
// Verify prompt_id exists
async function verifyPromptId(promptId) {
  try {
    const response = await fetch(`/api/inpaint/result/${promptId}`);
    if (response.status === 404) {
      console.log("Workflow still processing or prompt_id invalid");
    } else if (response.ok) {
      console.log("Workflow already completed");
    }
  } catch (error) {
    console.error("Error checking prompt status:", error);
  }
}

// Add timeout for progress updates
class TimeoutProgressTracker extends RobustProgressTracker {
  constructor(promptId, callbacks, progressTimeout = 60000) {
    super(promptId, callbacks);
    this.progressTimeout = progressTimeout;
    this.progressTimer = null;
  }

  handleMessage(data) {
    // Reset progress timeout on any message
    this.resetProgressTimeout();
    super.handleMessage(data);
  }

  resetProgressTimeout() {
    if (this.progressTimer) {
      clearTimeout(this.progressTimer);
    }

    this.progressTimer = setTimeout(() => {
      console.warn("No progress updates received, checking status...");
      this.checkWorkflowStatus();
    }, this.progressTimeout);
  }

  async checkWorkflowStatus() {
    try {
      const response = await fetch(`/api/inpaint/result/${this.promptId}`);
      if (response.ok) {
        // Workflow completed without final progress message
        this.callbacks.onComplete?.();
        this.disconnect();
      }
    } catch (error) {
      this.callbacks.onError?.("Progress timeout and status check failed");
    }
  }
}
```

### Performance Optimization

#### 1. Message Throttling

```javascript
class ThrottledProgressTracker extends RobustProgressTracker {
  constructor(promptId, callbacks, throttleMs = 100) {
    super(promptId, callbacks);
    this.throttleMs = throttleMs;
    this.lastProgressUpdate = 0;
    this.pendingProgress = null;
  }

  handleMessage(data) {
    if (data.type === "progress") {
      const now = Date.now();
      if (now - this.lastProgressUpdate >= this.throttleMs) {
        this.lastProgressUpdate = now;
        super.handleMessage(data);
        this.pendingProgress = null;
      } else {
        // Store latest progress for delayed update
        this.pendingProgress = data;
        setTimeout(() => {
          if (this.pendingProgress) {
            super.handleMessage(this.pendingProgress);
            this.pendingProgress = null;
            this.lastProgressUpdate = Date.now();
          }
        }, this.throttleMs - (now - this.lastProgressUpdate));
      }
    } else {
      super.handleMessage(data);
    }
  }
}
```

#### 2. Memory Management

```javascript
class ManagedProgressTracker extends RobustProgressTracker {
  constructor(promptId, callbacks) {
    super(promptId, callbacks);
    this.messageHistory = [];
    this.maxHistorySize = 100;
  }

  handleMessage(data) {
    // Store message history for debugging
    this.messageHistory.push({
      timestamp: Date.now(),
      message: data,
    });

    // Limit history size
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory.shift();
    }

    super.handleMessage(data);
  }

  getMessageHistory() {
    return this.messageHistory.slice();
  }

  disconnect() {
    // Clear history on disconnect
    this.messageHistory = [];
    super.disconnect();
  }
}
```

## Summary

The WebSocket progress tracking system provides:

1. **Real-time Updates**: Live progress monitoring during workflow execution
2. **Robust Connection Management**: Automatic reconnection and error handling
3. **Framework Integration**: Ready-to-use hooks for React and Vue.js
4. **Fallback Mechanisms**: HTTP polling when WebSocket fails
5. **Performance Optimization**: Throttling and memory management

### Key Recommendations

- Always implement reconnection logic for production use
- Use HTTP polling as a fallback mechanism
- Throttle progress updates for better performance
- Clean up connections properly to prevent memory leaks
- Monitor connection health with heartbeat mechanisms
- Handle edge cases like completed workflows and invalid prompt IDs

For complete implementation examples, see the [Integration Guide](./integration-guide.md) and [Interactive Examples](./examples.md).
