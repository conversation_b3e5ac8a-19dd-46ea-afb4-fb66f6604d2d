# Configuration Guide

## Overview

This guide covers the configuration options for the ComfyUI Inpainting Backend API, including environment variables, server settings, and deployment considerations.

## Environment Variables

### Core Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `COMFY_SERVER` | string | `127.0.0.1:8188` | ComfyUI server address (host:port) |
| `INPUT_DIR` | string | `./input` | Directory for input file storage |
| `WORKFLOW_PATH` | string | `./Subject Destination API.json` | Path to ComfyUI workflow JSON file |

### Progress Tracking

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `PROGRESS_QUEUE_SIZE` | integer | `200` | Maximum size of progress message queue |

### Workflow Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `RANDOMIZE_SEED` | boolean | `true` | Whether to randomize seeds for each workflow |
| `NODE_SCENE_ID` | string | `18` | ComfyUI node ID for scene destination input |
| `NODE_FABRIC_ID` | string | `294` | ComfyUI node ID for fabric swatch input |
| `NODE_MASK_ID` | string | `303` | ComfyUI node ID for curtain mask input |
| `NODE_OUTPUT_ID` | string | `302` | ComfyUI node ID for final output |

## Configuration Files

### Environment File (.env)

Create a `.env` file in the project root:

```bash
# ComfyUI Server Configuration
COMFY_SERVER=127.0.0.1:8188

# File Storage
INPUT_DIR=./input

# Workflow Configuration
WORKFLOW_PATH=./Subject Destination API.json
RANDOMIZE_SEED=true

# Node IDs (update based on your workflow)
NODE_SCENE_ID=18
NODE_FABRIC_ID=294
NODE_MASK_ID=303
NODE_OUTPUT_ID=302

# Progress Tracking
PROGRESS_QUEUE_SIZE=200
```

### Production Environment

```bash
# Production settings
COMFY_SERVER=comfyui-server:8188
INPUT_DIR=/app/data/input
WORKFLOW_PATH=/app/workflows/production.json
RANDOMIZE_SEED=true
PROGRESS_QUEUE_SIZE=500

# Node IDs for production workflow
NODE_SCENE_ID=18
NODE_FABRIC_ID=294
NODE_MASK_ID=303
NODE_OUTPUT_ID=302
```

### Development Environment

```bash
# Development settings
COMFY_SERVER=localhost:8188
INPUT_DIR=./dev_input
WORKFLOW_PATH=./dev_workflow.json
RANDOMIZE_SEED=false
PROGRESS_QUEUE_SIZE=50

# Debug node IDs
NODE_SCENE_ID=18
NODE_FABRIC_ID=294
NODE_MASK_ID=303
NODE_OUTPUT_ID=302
```

## Server Configuration

### FastAPI Settings

The API server can be configured through command-line arguments or environment variables:

```bash
# Start server with custom settings
uvicorn app.main:app \
  --host 0.0.0.0 \
  --port 8000 \
  --workers 4 \
  --reload
```

### CORS Configuration

CORS is currently configured to allow all origins. For production, update in `app/main.py`:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com", "https://app.yourdomain.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

## ComfyUI Integration

### ComfyUI Server Setup

Ensure ComfyUI is running and accessible:

```bash
# Check ComfyUI server status
curl http://127.0.0.1:8188/system_stats

# Expected response indicates server is running
```

### Workflow Configuration

The workflow JSON file must contain the required node IDs. Verify your workflow structure:

```python
import json

# Load and inspect workflow
with open('Subject Destination API.json', 'r') as f:
    workflow = json.load(f)

# Check required nodes exist
required_nodes = ['18', '294', '303', '302']
for node_id in required_nodes:
    if node_id not in workflow:
        print(f"Missing required node: {node_id}")
    else:
        print(f"Node {node_id}: {workflow[node_id].get('class_type', 'Unknown')}")
```

### Node ID Mapping

Update node IDs based on your ComfyUI workflow:

1. **Scene Input Node** (`NODE_SCENE_ID`): Load Image node for destination scene
2. **Fabric Input Node** (`NODE_FABRIC_ID`): Load Image node for fabric swatch
3. **Mask Input Node** (`NODE_MASK_ID`): Load Image node for curtain mask
4. **Output Node** (`NODE_OUTPUT_ID`): Save Image node for final result

## Directory Structure

### Required Directories

```
project_root/
├── input/                 # Input files directory
├── static/               # Static files (optional)
├── app/                  # Application code
├── Subject Destination API.json  # Workflow file
└── .env                  # Environment configuration
```

### Input Directory Setup

```bash
# Create input directory with proper permissions
mkdir -p input
chmod 755 input

# For production with Docker
mkdir -p /app/data/input
chown app:app /app/data/input
chmod 755 /app/data/input
```

## Docker Configuration

### Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application
COPY app/ ./app/
COPY Subject\ Destination\ API.json ./

# Create directories
RUN mkdir -p input static

# Set environment variables
ENV COMFY_SERVER=comfyui:8188
ENV INPUT_DIR=/app/input
ENV WORKFLOW_PATH=/app/Subject\ Destination\ API.json

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - COMFY_SERVER=comfyui:8188
      - INPUT_DIR=/app/input
      - WORKFLOW_PATH=/app/Subject Destination API.json
      - RANDOMIZE_SEED=true
    volumes:
      - ./input:/app/input
      - ./workflows:/app/workflows
    depends_on:
      - comfyui

  comfyui:
    image: comfyui/comfyui:latest
    ports:
      - "8188:8188"
    volumes:
      - ./comfyui_models:/app/models
      - ./comfyui_output:/app/output
```

## Security Configuration

### File Upload Security

```python
# Configure file upload limits
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.webp'}

# Validate file uploads
def validate_upload(file):
    # Check file size
    if file.size > MAX_FILE_SIZE:
        raise ValueError("File too large")
    
    # Check file extension
    ext = Path(file.filename).suffix.lower()
    if ext not in ALLOWED_EXTENSIONS:
        raise ValueError("Invalid file type")
```

### Input Validation

```python
# Sanitize file paths
def sanitize_filename(filename):
    # Remove path components
    filename = os.path.basename(filename)
    
    # Remove dangerous characters
    filename = re.sub(r'[^\w\-_\.]', '', filename)
    
    return filename
```

## Performance Configuration

### Concurrency Settings

```bash
# Production server with multiple workers
uvicorn app.main:app \
  --host 0.0.0.0 \
  --port 8000 \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker
```

### Memory Management

```python
# Configure progress queue size based on expected load
PROGRESS_QUEUE_SIZE = 1000  # For high-traffic scenarios

# Implement cleanup for old progress data
import asyncio
from datetime import datetime, timedelta

async def cleanup_old_progress():
    while True:
        # Clean up progress data older than 1 hour
        cutoff = datetime.now() - timedelta(hours=1)
        # Implementation depends on your storage mechanism
        await asyncio.sleep(300)  # Run every 5 minutes
```

## Monitoring Configuration

### Health Check Endpoint

The `/api/health` endpoint provides basic health information. For production monitoring:

```python
# Enhanced health check
@router.get("/health/detailed")
async def detailed_health():
    return {
        "status": "ok",
        "timestamp": datetime.utcnow().isoformat(),
        "comfyui_status": await check_comfyui_connection(),
        "disk_space": get_disk_usage(),
        "memory_usage": get_memory_usage()
    }
```

### Logging Configuration

```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api.log'),
        logging.StreamHandler()
    ]
)
```

## Troubleshooting Configuration Issues

### Common Problems

1. **ComfyUI Connection Failed**
   - Check `COMFY_SERVER` environment variable
   - Verify ComfyUI is running on specified port
   - Test connectivity: `curl http://127.0.0.1:8188/system_stats`

2. **File Upload Errors**
   - Verify `INPUT_DIR` exists and is writable
   - Check file permissions: `ls -la input/`
   - Ensure sufficient disk space

3. **Workflow Errors**
   - Validate `WORKFLOW_PATH` points to valid JSON file
   - Check node IDs match your workflow
   - Verify workflow loads in ComfyUI interface

4. **WebSocket Issues**
   - Check firewall settings for WebSocket connections
   - Verify proxy configuration allows WebSocket upgrades
   - Test WebSocket connectivity separately

### Configuration Validation

```python
# Validate configuration on startup
def validate_config():
    errors = []
    
    # Check ComfyUI connection
    try:
        response = requests.get(f"http://{settings.COMFY_SERVER}/system_stats", timeout=5)
        if not response.ok:
            errors.append(f"ComfyUI server not responding: {settings.COMFY_SERVER}")
    except Exception as e:
        errors.append(f"Cannot connect to ComfyUI: {e}")
    
    # Check input directory
    if not settings.INPUT_DIR.exists():
        errors.append(f"Input directory does not exist: {settings.INPUT_DIR}")
    elif not os.access(settings.INPUT_DIR, os.W_OK):
        errors.append(f"Input directory not writable: {settings.INPUT_DIR}")
    
    # Check workflow file
    if not settings.WORKFLOW_PATH.exists():
        errors.append(f"Workflow file not found: {settings.WORKFLOW_PATH}")
    
    if errors:
        raise RuntimeError("Configuration validation failed:\n" + "\n".join(errors))
```

This configuration guide provides all the necessary information to properly set up and configure the ComfyUI Inpainting Backend API for various deployment scenarios.
