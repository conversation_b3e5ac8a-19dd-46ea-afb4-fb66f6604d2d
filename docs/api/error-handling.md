# Error Handling Guide

## Overview

This guide provides comprehensive information about error handling in the ComfyUI Inpainting Backend API, including error codes, common scenarios, troubleshooting steps, and best practices for robust error handling.

## HTTP Status Codes

### Success Codes

| Code | Status | Description | Usage |
|------|--------|-------------|-------|
| 200 | OK | Request successful | All successful operations |

### Client Error Codes

| Code | Status | Description | Common Causes |
|------|--------|-------------|---------------|
| 400 | Bad Request | Invalid request parameters | Missing files, invalid filenames, malformed JSON |
| 404 | Not Found | Resource not found | Invalid prompt_id, no results available |

### Server Error Codes

| Code | Status | Description | Common Causes |
|------|--------|-------------|---------------|
| 500 | Internal Server Error | Server-side processing error | Workflow patching failed, configuration issues |
| 502 | Bad Gateway | Upstream service error | Failed to fetch results from ComfyUI |
| 503 | Service Unavailable | Service temporarily unavailable | ComfyUI server unavailable, upload failures |

## Error Response Format

All API errors follow a consistent JSON format:

```json
{
  "detail": "Human-readable error message with specific details"
}
```

### Example Error Responses

#### File Not Found (400)
```json
{
  "detail": "Input file not found: fabric_swatch.png"
}
```

#### Invalid File Type (400)
```json
{
  "detail": "fabric_swatch must be an image file (PNG, JPG, JPEG, or WebP)"
}
```

#### ComfyUI Connection Failed (503)
```json
{
  "detail": "Failed to upload inputs to ComfyUI: Connection refused"
}
```

#### No Results Available (404)
```json
{
  "detail": "No outputs found for this prompt"
}
```

## Common Error Scenarios

### 1. File Upload Errors

#### Invalid File Type
**Error**: `fabric_swatch must be an image file (PNG, JPG, JPEG, or WebP)`
**Cause**: Uploaded file has unsupported MIME type
**Solution**: 
```javascript
// Validate file type before upload
function validateFileType(file) {
  const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} not supported. Use PNG, JPG, JPEG, or WebP.`);
  }
}
```

#### File Size Too Large
**Error**: `Failed to save uploaded files: File too large`
**Cause**: Uploaded file exceeds server limits
**Solution**:
```javascript
// Check file size before upload
function validateFileSize(file, maxSizeMB = 10) {
  const maxBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxBytes) {
    throw new Error(`File size ${(file.size / 1024 / 1024).toFixed(2)}MB exceeds ${maxSizeMB}MB limit`);
  }
}
```

### 2. Workflow Execution Errors

#### Missing Input Files
**Error**: `Input file not found: fabric_swatch.png`
**Cause**: Referenced file doesn't exist in input directory
**Solution**:
```javascript
// Verify files exist before starting workflow
async function verifyFilesExist(filenames) {
  for (const filename of filenames) {
    try {
      // This would be a custom endpoint to check file existence
      const response = await fetch(`/api/files/check/${filename}`);
      if (!response.ok) {
        throw new Error(`File not found: ${filename}`);
      }
    } catch (error) {
      throw new Error(`Cannot verify file existence: ${filename}`);
    }
  }
}
```

#### Invalid Filename Format
**Error**: `Filenames must not contain directories`
**Cause**: Filename contains path separators
**Solution**:
```javascript
// Sanitize filenames
function sanitizeFilename(filename) {
  // Extract just the filename without path
  const sanitized = filename.replace(/^.*[\\\/]/, '');
  
  // Validate no remaining path characters
  if (sanitized.includes('/') || sanitized.includes('\\')) {
    throw new Error('Invalid filename format');
  }
  
  return sanitized;
}
```

### 3. ComfyUI Integration Errors

#### ComfyUI Server Unavailable
**Error**: `Failed to upload inputs to ComfyUI: Connection refused`
**Cause**: ComfyUI server is not running or unreachable
**Solution**:
```javascript
// Check ComfyUI server health before operations
async function checkComfyUIHealth(serverAddress = '127.0.0.1:8188') {
  try {
    const response = await fetch(`http://${serverAddress}/system_stats`, {
      timeout: 5000
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Use with retry logic
async function withComfyUIHealthCheck(operation) {
  const isHealthy = await checkComfyUIHealth();
  if (!isHealthy) {
    throw new Error('ComfyUI server is not available. Please ensure it is running.');
  }
  return operation();
}
```

#### Workflow Patching Failed
**Error**: `Failed to patch workflow: One or more required node IDs not found in workflow JSON`
**Cause**: Workflow JSON doesn't contain expected node structure
**Solution**:
```javascript
// Validate workflow structure before patching
function validateWorkflowStructure(workflow, requiredNodes) {
  const missingNodes = requiredNodes.filter(nodeId => !workflow[nodeId]);
  if (missingNodes.length > 0) {
    throw new Error(`Workflow missing required nodes: ${missingNodes.join(', ')}`);
  }
}
```

### 4. Result Retrieval Errors

#### No Results Available
**Error**: `No outputs found for this prompt`
**Cause**: Workflow hasn't completed or failed to generate outputs
**Solution**:
```javascript
// Poll for results with timeout
async function waitForResults(promptId, maxWaitMs = 300000) {
  const startTime = Date.now();
  const pollInterval = 2000;
  
  while (Date.now() - startTime < maxWaitMs) {
    try {
      const response = await fetch(`/api/inpaint/result/${promptId}`);
      if (response.ok) {
        return await response.blob();
      } else if (response.status === 404) {
        // Still processing, continue waiting
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        continue;
      } else {
        throw new Error(`Result retrieval failed: ${response.status}`);
      }
    } catch (error) {
      if (Date.now() - startTime >= maxWaitMs) {
        throw new Error('Timeout waiting for results');
      }
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
  }
  
  throw new Error('Timeout waiting for results');
}
```

## WebSocket Error Handling

### Connection Errors

#### Connection Refused
**Cause**: WebSocket server unavailable or incorrect URL
**Solution**:
```javascript
class RobustWebSocket {
  constructor(url, options = {}) {
    this.url = url;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.reconnectDelay = options.reconnectDelay || 1000;
    this.reconnectAttempts = 0;
  }

  connect() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.url);
      
      const timeout = setTimeout(() => {
        this.ws.close();
        reject(new Error('WebSocket connection timeout'));
      }, 10000);

      this.ws.onopen = () => {
        clearTimeout(timeout);
        this.reconnectAttempts = 0;
        resolve();
      };

      this.ws.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('WebSocket connection failed'));
      };

      this.ws.onclose = (event) => {
        if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.attemptReconnection();
        }
      };
    });
  }

  attemptReconnection() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    setTimeout(() => {
      this.connect().catch(() => {
        // Reconnection failed, will try again if attempts remain
      });
    }, delay);
  }
}
```

### Message Parsing Errors

**Cause**: Invalid JSON in WebSocket messages
**Solution**:
```javascript
ws.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data);
    handleMessage(data);
  } catch (error) {
    console.error('Failed to parse WebSocket message:', error);
    console.error('Raw message:', event.data);
    // Continue processing other messages
  }
};
```

## Error Handling Best Practices

### 1. Comprehensive Error Handling

```javascript
class APIError extends Error {
  constructor(message, status, code) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.code = code;
  }
}

async function handleAPIRequest(requestFn) {
  try {
    const response = await requestFn();
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new APIError(
        errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData.code
      );
    }
    
    return response;
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }
    
    // Network or other errors
    throw new APIError(
      `Network error: ${error.message}`,
      0,
      'NETWORK_ERROR'
    );
  }
}
```

### 2. Retry Logic with Exponential Backoff

```javascript
async function withRetry(fn, options = {}) {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    retryCondition = (error) => error.status >= 500
  } = options;

  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors or if retry condition not met
      if (!retryCondition(error) || attempt === maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}
```

### 3. User-Friendly Error Messages

```javascript
function getUserFriendlyErrorMessage(error) {
  if (error.status === 400) {
    if (error.message.includes('file not found')) {
      return 'One or more uploaded files could not be found. Please try uploading again.';
    }
    if (error.message.includes('must be an image file')) {
      return 'Please upload only image files (PNG, JPG, JPEG, or WebP format).';
    }
  }
  
  if (error.status === 503) {
    return 'The service is temporarily unavailable. Please try again in a few moments.';
  }
  
  if (error.status === 404) {
    return 'The requested resource was not found. The workflow may still be processing.';
  }
  
  if (error.code === 'NETWORK_ERROR') {
    return 'Unable to connect to the server. Please check your internet connection.';
  }
  
  return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
}
```

### 4. Error Logging and Monitoring

```javascript
class ErrorLogger {
  static log(error, context = {}) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      message: error.message,
      status: error.status,
      code: error.code,
      stack: error.stack,
      context
    };
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('API Error:', errorInfo);
    }
    
    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: send to monitoring service
      // monitoringService.logError(errorInfo);
    }
  }
}

// Usage
try {
  await apiCall();
} catch (error) {
  ErrorLogger.log(error, { 
    operation: 'file_upload',
    userId: currentUser.id,
    sessionId: sessionId
  });
  throw error;
}
```

## Troubleshooting Checklist

### Before Making API Calls

- [ ] Verify API server is running (`/api/health`)
- [ ] Check ComfyUI server availability
- [ ] Validate file formats and sizes
- [ ] Ensure proper authentication (if required)

### When Errors Occur

- [ ] Check HTTP status code and error message
- [ ] Verify request format and required parameters
- [ ] Test with minimal/mock data
- [ ] Check server logs for additional details
- [ ] Verify network connectivity

### For WebSocket Issues

- [ ] Test WebSocket connectivity separately
- [ ] Check for firewall/proxy blocking WebSocket connections
- [ ] Verify correct WebSocket URL format
- [ ] Implement reconnection logic
- [ ] Use HTTP polling as fallback

### Performance Issues

- [ ] Monitor request/response times
- [ ] Check for concurrent request limits
- [ ] Verify file sizes are reasonable
- [ ] Test with smaller datasets
- [ ] Implement request queuing if needed

This comprehensive error handling guide provides the foundation for building robust applications that gracefully handle all types of errors that may occur when integrating with the ComfyUI Inpainting Backend API.
