# Integration Guide

## Overview

This guide provides step-by-step instructions for integrating with the ComfyUI Inpainting Backend API. It includes practical examples, error handling patterns, and best practices for frontend applications.

## Table of Contents

1. [Quick Start](#quick-start)
2. [JavaScript/TypeScript Integration](#javascripttypescript-integration)
3. [Error Handling Patterns](#error-handling-patterns)
4. [Best Practices](#best-practices)
5. [Complete Examples](#complete-examples)

## Quick Start

### Basic Workflow

1. **Upload files** (optional, if not using run-with-files)
2. **Start workflow** with uploaded files or direct upload
3. **Connect to WebSocket** for progress tracking
4. **Download result** when complete

### Minimal Example

```javascript
// Option 1: Upload and run in one step
async function processImages(fabricFile, sceneFile, maskFile) {
  const formData = new FormData();
  formData.append("fabric_swatch", fabricFile);
  formData.append("scene_destination", sceneFile);
  formData.append("curtain_mask", maskFile);

  const response = await fetch("/api/inpaint/run-with-files", {
    method: "POST",
    body: formData,
  });

  const result = await response.json();
  return result.prompt_id;
}
```

## JavaScript/TypeScript Integration

### TypeScript Interfaces

```typescript
interface RunRequest {
  fabric_swatch: string;
  scene_destination: string;
  curtain_mask: string;
  comfy_server?: string;
}

interface RunResponse {
  prompt_id: string;
  progress_ws: string;
  result_url: string;
}

interface ProgressEvent {
  type: "progress" | "executing" | "error";
  data?: {
    value: number;
    max: number;
    prompt_id: string;
    node: string | null;
  };
  message?: string;
}

interface UploadResponse {
  message: string;
  session_id: string;
  fabric_swatch: string;
  scene_destination: string;
  curtain_mask: string;
}
```

### API Client Class

```typescript
class ComfyUIClient {
  private baseUrl: string;

  constructor(baseUrl: string = "http://localhost:8000") {
    this.baseUrl = baseUrl;
  }

  async healthCheck(): Promise<{ status: string }> {
    const response = await fetch(`${this.baseUrl}/api/health`);
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }
    return response.json();
  }

  async uploadFiles(
    fabricFile: File,
    sceneFile: File,
    maskFile: File
  ): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append("fabric_swatch", fabricFile);
    formData.append("scene_destination", sceneFile);
    formData.append("curtain_mask", maskFile);

    const response = await fetch(`${this.baseUrl}/api/upload`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || "Upload failed");
    }

    return response.json();
  }

  async startWorkflow(request: RunRequest): Promise<RunResponse> {
    const response = await fetch(`${this.baseUrl}/api/inpaint/run`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || "Workflow start failed");
    }

    return response.json();
  }

  async runWithFiles(
    fabricFile: File,
    sceneFile: File,
    maskFile: File,
    comfyServer?: string
  ): Promise<RunResponse> {
    const formData = new FormData();
    formData.append("fabric_swatch", fabricFile);
    formData.append("scene_destination", sceneFile);
    formData.append("curtain_mask", maskFile);

    if (comfyServer) {
      formData.append("comfy_server", comfyServer);
    }

    const response = await fetch(`${this.baseUrl}/api/inpaint/run-with-files`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || "Workflow execution failed");
    }

    return response.json();
  }

  connectProgress(
    promptId: string,
    onProgress: (event: ProgressEvent) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): WebSocket {
    const wsUrl = `ws://${this.baseUrl.replace(
      "http://",
      ""
    )}/ws/progress/${promptId}`;
    const ws = new WebSocket(wsUrl);

    ws.onmessage = (event) => {
      const data: ProgressEvent = JSON.parse(event.data);

      if (data.type === "executing" && data.data?.node === null) {
        onComplete();
        ws.close();
      } else {
        onProgress(data);
      }
    };

    ws.onerror = () => {
      onError("WebSocket connection failed");
    };

    ws.onclose = () => {
      console.log("Progress stream disconnected");
    };

    return ws;
  }

  async downloadResult(promptId: string): Promise<Blob> {
    const response = await fetch(
      `${this.baseUrl}/api/inpaint/result/${promptId}`
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || "Failed to download result");
    }

    return response.blob();
  }
}
```

### Usage Example

```typescript
const client = new ComfyUIClient();

async function processInpainting() {
  try {
    // Check API health
    await client.healthCheck();
    console.log("API is healthy");

    // Get file inputs (from file input elements)
    const fabricFile = document.getElementById("fabric")?.files[0];
    const sceneFile = document.getElementById("scene")?.files[0];
    const maskFile = document.getElementById("mask")?.files[0];

    if (!fabricFile || !sceneFile || !maskFile) {
      throw new Error("Please select all required files");
    }

    // Start workflow
    const result = await client.runWithFiles(fabricFile, sceneFile, maskFile);
    console.log("Workflow started:", result.prompt_id);

    // Track progress
    const ws = client.connectProgress(
      result.prompt_id,
      (event) => {
        if (event.type === "progress" && event.data) {
          const percentage = (event.data.value / event.data.max) * 100;
          updateProgressBar(percentage);
        }
      },
      async () => {
        console.log("Workflow completed!");
        const resultBlob = await client.downloadResult(result.prompt_id);
        displayResult(resultBlob);
      },
      (error) => {
        console.error("Progress error:", error);
        showError(error);
      }
    );
  } catch (error) {
    console.error("Processing failed:", error);
    showError(error.message);
  }
}
```

## Error Handling Patterns

### Comprehensive Error Handling

```typescript
class APIError extends Error {
  constructor(message: string, public status: number, public code?: string) {
    super(message);
    this.name = "APIError";
  }
}

async function handleAPIRequest<T>(
  requestFn: () => Promise<Response>
): Promise<T> {
  try {
    const response = await requestFn();

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new APIError(
        errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData.code
      );
    }

    return response.json();
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }

    // Network or other errors
    throw new APIError(`Network error: ${error.message}`, 0);
  }
}
```

### Retry Logic with Exponential Backoff

```typescript
async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      // Don't retry on client errors (4xx)
      if (
        error instanceof APIError &&
        error.status >= 400 &&
        error.status < 500
      ) {
        throw error;
      }

      if (attempt === maxRetries) {
        break;
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
```

### WebSocket Error Handling

```typescript
class ProgressTracker {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(
    promptId: string,
    onProgress: (event: ProgressEvent) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): void {
    const wsUrl = `ws://localhost:8000/ws/progress/${promptId}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log("Progress WebSocket connected");
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      try {
        const data: ProgressEvent = JSON.parse(event.data);

        if (data.type === "executing" && data.data?.node === null) {
          onComplete();
          this.disconnect();
        } else {
          onProgress(data);
        }
      } catch (error) {
        console.error("Failed to parse progress message:", error);
      }
    };

    this.ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      onError("Connection error occurred");
    };

    this.ws.onclose = (event) => {
      if (event.wasClean) {
        console.log("Progress stream closed cleanly");
        return;
      }

      // Attempt reconnection
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        const delay =
          this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

        console.log(
          `Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`
        );
        setTimeout(() => {
          this.connect(promptId, onProgress, onComplete, onError);
        }, delay);
      } else {
        onError("Failed to maintain connection after multiple attempts");
      }
    };
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

### File Validation

```typescript
interface FileValidationOptions {
  maxSize: number; // in bytes
  allowedTypes: string[];
  maxDimensions?: { width: number; height: number };
}

async function validateFile(
  file: File,
  options: FileValidationOptions
): Promise<void> {
  // Check file size
  if (file.size > options.maxSize) {
    throw new Error(
      `File size exceeds ${options.maxSize / (1024 * 1024)}MB limit`
    );
  }

  // Check file type
  if (!options.allowedTypes.includes(file.type)) {
    throw new Error(
      `File type ${
        file.type
      } not allowed. Allowed types: ${options.allowedTypes.join(", ")}`
    );
  }

  // Check image dimensions (if specified)
  if (options.maxDimensions && file.type.startsWith("image/")) {
    const dimensions = await getImageDimensions(file);
    if (
      dimensions.width > options.maxDimensions.width ||
      dimensions.height > options.maxDimensions.height
    ) {
      throw new Error(
        `Image dimensions exceed ${options.maxDimensions.width}x${options.maxDimensions.height}`
      );
    }
  }
}

function getImageDimensions(
  file: File
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = () => {
      reject(new Error("Failed to load image"));
    };
    img.src = URL.createObjectURL(file);
  });
}
```

## Best Practices

### 1. File Upload Optimization

```typescript
// Validate files before upload
const validationOptions: FileValidationOptions = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
  maxDimensions: { width: 4096, height: 4096 },
};

async function optimizedUpload(files: File[]) {
  // Validate all files first
  for (const file of files) {
    await validateFile(file, validationOptions);
  }

  // Show progress during upload
  const formData = new FormData();
  files.forEach((file, index) => {
    const fieldNames = ["fabric_swatch", "scene_destination", "curtain_mask"];
    formData.append(fieldNames[index], file);
  });

  return withRetry(() =>
    fetch("/api/inpaint/run-with-files", {
      method: "POST",
      body: formData,
    })
  );
}
```

### 2. Progress Tracking Best Practices

```typescript
class InpaintingWorkflow {
  private progressTracker: ProgressTracker;
  private currentPromptId: string | null = null;

  constructor(private onProgress: (percentage: number) => void) {
    this.progressTracker = new ProgressTracker();
  }

  async start(
    fabricFile: File,
    sceneFile: File,
    maskFile: File
  ): Promise<string> {
    try {
      const client = new ComfyUIClient();
      const result = await client.runWithFiles(fabricFile, sceneFile, maskFile);

      this.currentPromptId = result.prompt_id;
      this.trackProgress(result.prompt_id);

      return result.prompt_id;
    } catch (error) {
      throw new Error(`Failed to start workflow: ${error.message}`);
    }
  }

  private trackProgress(promptId: string): void {
    this.progressTracker.connect(
      promptId,
      (event) => {
        if (event.type === "progress" && event.data) {
          const percentage = (event.data.value / event.data.max) * 100;
          this.onProgress(Math.round(percentage));
        }
      },
      () => {
        this.onProgress(100);
        console.log("Workflow completed successfully");
      },
      (error) => {
        console.error("Progress tracking error:", error);
        // Implement fallback polling if needed
        this.fallbackToPolling(promptId);
      }
    );
  }

  private async fallbackToPolling(promptId: string): Promise<void> {
    // Implement HTTP polling as fallback
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;

    const poll = async (): Promise<void> => {
      try {
        const client = new ComfyUIClient();
        await client.downloadResult(promptId);
        this.onProgress(100);
        console.log("Workflow completed (detected via polling)");
      } catch (error) {
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          throw new Error("Workflow timeout - please check manually");
        }
      }
    };

    setTimeout(poll, 5000);
  }

  cancel(): void {
    this.progressTracker.disconnect();
    this.currentPromptId = null;
  }
}
```

### 3. Memory Management

```typescript
class ResourceManager {
  private objectUrls: string[] = [];
  private activeConnections: WebSocket[] = [];

  createObjectURL(blob: Blob): string {
    const url = URL.createObjectURL(blob);
    this.objectUrls.push(url);
    return url;
  }

  addConnection(ws: WebSocket): void {
    this.activeConnections.push(ws);
  }

  cleanup(): void {
    // Revoke object URLs
    this.objectUrls.forEach((url) => URL.revokeObjectURL(url));
    this.objectUrls = [];

    // Close WebSocket connections
    this.activeConnections.forEach((ws) => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    });
    this.activeConnections = [];
  }
}

// Use in component cleanup
const resourceManager = new ResourceManager();

// When component unmounts or page unloads
window.addEventListener("beforeunload", () => {
  resourceManager.cleanup();
});
```

### 4. User Experience Enhancements

```typescript
interface UIState {
  isUploading: boolean;
  isProcessing: boolean;
  progress: number;
  error: string | null;
  result: string | null;
}

class InpaintingUI {
  private state: UIState = {
    isUploading: false,
    isProcessing: false,
    progress: 0,
    error: null,
    result: null,
  };

  async processFiles(files: FileList): Promise<void> {
    try {
      this.setState({ isUploading: true, error: null });

      // Validate files
      if (files.length !== 3) {
        throw new Error("Please select exactly 3 files");
      }

      const [fabricFile, sceneFile, maskFile] = Array.from(files);

      // Start processing
      this.setState({ isUploading: false, isProcessing: true });

      const workflow = new InpaintingWorkflow((progress) => {
        this.setState({ progress });
      });

      const promptId = await workflow.start(fabricFile, sceneFile, maskFile);

      // Wait for completion (handled by progress callback)
      // Result will be available when progress reaches 100%
    } catch (error) {
      this.setState({
        error: error.message,
        isUploading: false,
        isProcessing: false,
      });
    }
  }

  private setState(updates: Partial<UIState>): void {
    this.state = { ...this.state, ...updates };
    this.render();
  }

  private render(): void {
    // Update UI based on state
    const uploadButton = document.getElementById("upload-btn");
    const progressBar = document.getElementById("progress");
    const errorDiv = document.getElementById("error");

    if (uploadButton) {
      uploadButton.disabled = this.state.isUploading || this.state.isProcessing;
      uploadButton.textContent = this.state.isUploading
        ? "Uploading..."
        : this.state.isProcessing
        ? "Processing..."
        : "Start Processing";
    }

    if (progressBar) {
      progressBar.style.width = `${this.state.progress}%`;
    }

    if (errorDiv) {
      errorDiv.textContent = this.state.error || "";
      errorDiv.style.display = this.state.error ? "block" : "none";
    }
  }
}
```

## Complete Examples

### React Component Example

```tsx
import React, { useState, useCallback, useRef } from "react";

interface InpaintingComponentProps {
  onResult?: (imageUrl: string) => void;
}

const InpaintingComponent: React.FC<InpaintingComponentProps> = ({
  onResult,
}) => {
  const [state, setState] = useState({
    isProcessing: false,
    progress: 0,
    error: null as string | null,
    result: null as string | null,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const workflowRef = useRef<InpaintingWorkflow | null>(null);

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (!files || files.length !== 3) {
        setState((prev) => ({
          ...prev,
          error: "Please select exactly 3 files",
        }));
        return;
      }

      try {
        setState((prev) => ({
          ...prev,
          isProcessing: true,
          error: null,
          progress: 0,
        }));

        const [fabricFile, sceneFile, maskFile] = Array.from(files);

        // Validate files
        const validationOptions = {
          maxSize: 10 * 1024 * 1024,
          allowedTypes: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
        };

        for (const file of [fabricFile, sceneFile, maskFile]) {
          await validateFile(file, validationOptions);
        }

        // Start workflow
        const workflow = new InpaintingWorkflow((progress) => {
          setState((prev) => ({ ...prev, progress }));
        });

        workflowRef.current = workflow;
        const promptId = await workflow.start(fabricFile, sceneFile, maskFile);

        // Download result when complete
        const client = new ComfyUIClient();
        const resultBlob = await client.downloadResult(promptId);
        const imageUrl = URL.createObjectURL(resultBlob);

        setState((prev) => ({
          ...prev,
          result: imageUrl,
          isProcessing: false,
          progress: 100,
        }));

        onResult?.(imageUrl);
      } catch (error) {
        setState((prev) => ({
          ...prev,
          error: error.message,
          isProcessing: false,
        }));
      }
    },
    [onResult]
  );

  const handleCancel = useCallback(() => {
    workflowRef.current?.cancel();
    setState((prev) => ({ ...prev, isProcessing: false, progress: 0 }));
  }, []);

  return (
    <div className="inpainting-component">
      <div className="upload-section">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileUpload}
          disabled={state.isProcessing}
        />

        {state.isProcessing && (
          <button onClick={handleCancel} className="cancel-btn">
            Cancel
          </button>
        )}
      </div>

      {state.isProcessing && (
        <div className="progress-section">
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${state.progress}%` }}
            />
          </div>
          <span>{state.progress}%</span>
        </div>
      )}

      {state.error && <div className="error-message">{state.error}</div>}

      {state.result && (
        <div className="result-section">
          <img src={state.result} alt="Inpainting result" />
          <a href={state.result} download="result.png">
            Download Result
          </a>
        </div>
      )}
    </div>
  );
};

export default InpaintingComponent;
```

### Vanilla JavaScript Example

```html
<!DOCTYPE html>
<html>
  <head>
    <title>ComfyUI Inpainting Demo</title>
    <style>
      .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .upload-area {
        border: 2px dashed #ccc;
        padding: 20px;
        text-align: center;
      }
      .progress-bar {
        width: 100%;
        height: 20px;
        background: #f0f0f0;
        margin: 10px 0;
      }
      .progress-fill {
        height: 100%;
        background: #4caf50;
        transition: width 0.3s;
      }
      .error {
        color: red;
        margin: 10px 0;
      }
      .result {
        margin: 20px 0;
      }
      .result img {
        max-width: 100%;
        height: auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>ComfyUI Inpainting Demo</h1>

      <div class="upload-area">
        <input type="file" id="fileInput" multiple accept="image/*" />
        <p>Select fabric swatch, scene destination, and curtain mask images</p>
      </div>

      <div id="progressSection" style="display: none;">
        <div class="progress-bar">
          <div id="progressFill" class="progress-fill"></div>
        </div>
        <span id="progressText">0%</span>
        <button id="cancelBtn">Cancel</button>
      </div>

      <div id="errorSection" class="error" style="display: none;"></div>

      <div id="resultSection" class="result" style="display: none;">
        <h3>Result:</h3>
        <img id="resultImage" alt="Result" />
        <br />
        <a id="downloadLink" download="result.png">Download Result</a>
      </div>
    </div>

    <script>
      class InpaintingDemo {
        constructor() {
          this.fileInput = document.getElementById("fileInput");
          this.progressSection = document.getElementById("progressSection");
          this.progressFill = document.getElementById("progressFill");
          this.progressText = document.getElementById("progressText");
          this.cancelBtn = document.getElementById("cancelBtn");
          this.errorSection = document.getElementById("errorSection");
          this.resultSection = document.getElementById("resultSection");
          this.resultImage = document.getElementById("resultImage");
          this.downloadLink = document.getElementById("downloadLink");

          this.workflow = null;
          this.setupEventListeners();
        }

        setupEventListeners() {
          this.fileInput.addEventListener(
            "change",
            this.handleFileUpload.bind(this)
          );
          this.cancelBtn.addEventListener(
            "click",
            this.handleCancel.bind(this)
          );
        }

        async handleFileUpload(event) {
          const files = event.target.files;
          if (files.length !== 3) {
            this.showError("Please select exactly 3 files");
            return;
          }

          try {
            this.showProgress(true);
            this.hideError();
            this.hideResult();

            const [fabricFile, sceneFile, maskFile] = Array.from(files);

            // Start workflow
            this.workflow = new InpaintingWorkflow((progress) => {
              this.updateProgress(progress);
            });

            const promptId = await this.workflow.start(
              fabricFile,
              sceneFile,
              maskFile
            );

            // Download result
            const client = new ComfyUIClient();
            const resultBlob = await client.downloadResult(promptId);
            const imageUrl = URL.createObjectURL(resultBlob);

            this.showResult(imageUrl);
            this.showProgress(false);
          } catch (error) {
            this.showError(error.message);
            this.showProgress(false);
          }
        }

        handleCancel() {
          if (this.workflow) {
            this.workflow.cancel();
            this.workflow = null;
          }
          this.showProgress(false);
        }

        updateProgress(percentage) {
          this.progressFill.style.width = `${percentage}%`;
          this.progressText.textContent = `${percentage}%`;
        }

        showProgress(show) {
          this.progressSection.style.display = show ? "block" : "none";
          this.fileInput.disabled = show;
          if (!show) {
            this.updateProgress(0);
          }
        }

        showError(message) {
          this.errorSection.textContent = message;
          this.errorSection.style.display = "block";
        }

        hideError() {
          this.errorSection.style.display = "none";
        }

        showResult(imageUrl) {
          this.resultImage.src = imageUrl;
          this.downloadLink.href = imageUrl;
          this.resultSection.style.display = "block";
        }

        hideResult() {
          this.resultSection.style.display = "none";
        }
      }

      // Initialize demo when page loads
      document.addEventListener("DOMContentLoaded", () => {
        new InpaintingDemo();
      });
    </script>
  </body>
</html>
```

### Node.js Backend Integration

```javascript
const express = require("express");
const multer = require("multer");
const FormData = require("form-data");
const fetch = require("node-fetch");

const app = express();
const upload = multer({ dest: "uploads/" });

// Proxy endpoint for ComfyUI API
app.post(
  "/api/process-inpainting",
  upload.fields([
    { name: "fabric_swatch", maxCount: 1 },
    { name: "scene_destination", maxCount: 1 },
    { name: "curtain_mask", maxCount: 1 },
  ]),
  async (req, res) => {
    try {
      const { fabric_swatch, scene_destination, curtain_mask } = req.files;

      if (!fabric_swatch || !scene_destination || !curtain_mask) {
        return res.status(400).json({ error: "Missing required files" });
      }

      // Forward to ComfyUI API
      const formData = new FormData();
      formData.append(
        "fabric_swatch",
        fs.createReadStream(fabric_swatch[0].path)
      );
      formData.append(
        "scene_destination",
        fs.createReadStream(scene_destination[0].path)
      );
      formData.append(
        "curtain_mask",
        fs.createReadStream(curtain_mask[0].path)
      );

      const response = await fetch(
        "http://localhost:8000/api/inpaint/run-with-files",
        {
          method: "POST",
          body: formData,
        }
      );

      const result = await response.json();

      if (!response.ok) {
        return res.status(response.status).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error("Processing error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

app.listen(3000, () => {
  console.log("Proxy server running on port 3000");
});
```

## Summary

This integration guide provides:

1. **Complete TypeScript interfaces** for type safety
2. **Robust error handling** with retry logic and fallbacks
3. **WebSocket management** with reconnection capabilities
4. **File validation** and optimization techniques
5. **Memory management** best practices
6. **Real-world examples** for React, Vanilla JS, and Node.js

### Key Takeaways

- Always validate files before upload
- Implement proper error handling and retry logic
- Use WebSocket for real-time progress with HTTP polling fallback
- Manage resources (object URLs, WebSocket connections) properly
- Provide clear user feedback during processing
- Handle edge cases like network failures and timeouts

For more detailed information, refer to the [API Endpoints Reference](./endpoints.md) and [WebSocket Guide](./websocket-guide.md).
