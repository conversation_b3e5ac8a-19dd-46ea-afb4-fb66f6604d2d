# Interactive Documentation Examples

## Overview

This document provides interactive examples and live demonstrations of the ComfyUI Inpainting Backend API. These examples help you understand the data flow, test endpoints, and see real request/response patterns.

## Table of Contents

1. [Quick Test Examples](#quick-test-examples)
2. [Complete Workflow Examples](#complete-workflow-examples)
3. [Error Handling Demonstrations](#error-handling-demonstrations)
4. [Performance Testing](#performance-testing)
5. [Live API Explorer](#live-api-explorer)

## Quick Test Examples

### 1. Health Check Test

Test the basic API connectivity:

```bash
# Using curl
curl -X GET "http://localhost:8000/api/health"

# Expected response:
# {
#   "status": "ok"
# }
```

```javascript
// Using JavaScript
async function testHealth() {
  try {
    const response = await fetch("/api/health");
    const data = await response.json();
    console.log("Health check result:", data);
    return data.status === "ok";
  } catch (error) {
    console.error("Health check failed:", error);
    return false;
  }
}

// Run the test
testHealth().then((isHealthy) => {
  console.log("API is healthy:", isHealthy);
});
```

### 2. File Upload Test

Test file upload functionality:

```javascript
// Create test files (for demonstration)
function createTestFile(name, content = "test image data") {
  const blob = new Blob([content], { type: "image/png" });
  return new File([blob], name, { type: "image/png" });
}

async function testFileUpload() {
  const fabricFile = createTestFile("fabric_test.png");
  const sceneFile = createTestFile("scene_test.png");
  const maskFile = createTestFile("mask_test.png");

  const formData = new FormData();
  formData.append("fabric_swatch", fabricFile);
  formData.append("scene_destination", sceneFile);
  formData.append("curtain_mask", maskFile);

  try {
    const response = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (response.ok) {
      const result = await response.json();
      console.log("Upload successful:", result);
      return result;
    } else {
      const error = await response.json();
      console.error("Upload failed:", error);
      throw new Error(error.detail);
    }
  } catch (error) {
    console.error("Upload error:", error);
    throw error;
  }
}
```

### 3. WebSocket Connection Test

Test WebSocket connectivity:

```javascript
function testWebSocketConnection(promptId = "test-prompt-id") {
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(`ws://localhost:8000/ws/progress/${promptId}`);
    let connected = false;

    const timeout = setTimeout(() => {
      if (!connected) {
        ws.close();
        reject(new Error("WebSocket connection timeout"));
      }
    }, 5000);

    ws.onopen = () => {
      connected = true;
      clearTimeout(timeout);
      console.log("WebSocket connected successfully");
      ws.close();
      resolve(true);
    };

    ws.onerror = (error) => {
      clearTimeout(timeout);
      console.error("WebSocket connection failed:", error);
      reject(error);
    };

    ws.onclose = () => {
      if (connected) {
        console.log("WebSocket closed cleanly");
      }
    };
  });
}

// Test WebSocket connectivity
testWebSocketConnection()
  .then(() => console.log("WebSocket test passed"))
  .catch((error) => console.error("WebSocket test failed:", error));
```

## Complete Workflow Examples

### 1. End-to-End Workflow Test

Complete workflow from upload to result download:

```javascript
class WorkflowTester {
  constructor(baseUrl = "http://localhost:8000") {
    this.baseUrl = baseUrl;
    this.results = {
      health: null,
      upload: null,
      workflow: null,
      progress: [],
      result: null,
      errors: [],
    };
  }

  async runCompleteTest() {
    console.log("🚀 Starting complete workflow test...");

    try {
      // Step 1: Health check
      await this.testHealth();

      // Step 2: Upload files
      await this.testUpload();

      // Step 3: Start workflow
      await this.testWorkflowStart();

      // Step 4: Track progress
      await this.testProgressTracking();

      // Step 5: Download result
      await this.testResultDownload();

      console.log("✅ Complete workflow test passed!");
      return this.results;
    } catch (error) {
      console.error("❌ Workflow test failed:", error);
      this.results.errors.push(error.message);
      throw error;
    }
  }

  async testHealth() {
    console.log("📋 Testing health endpoint...");
    const response = await fetch(`${this.baseUrl}/api/health`);

    if (!response.ok) {
      throw new Error(`Health check failed: ${response.status}`);
    }

    this.results.health = await response.json();
    console.log("✅ Health check passed");
  }

  async testUpload() {
    console.log("📁 Testing file upload...");

    // Create mock files
    const files = {
      fabric_swatch: this.createMockFile("fabric.png"),
      scene_destination: this.createMockFile("scene.png"),
      curtain_mask: this.createMockFile("mask.png"),
    };

    const formData = new FormData();
    Object.entries(files).forEach(([key, file]) => {
      formData.append(key, file);
    });

    const response = await fetch(`${this.baseUrl}/api/upload`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Upload failed: ${error.detail}`);
    }

    this.results.upload = await response.json();
    console.log("✅ File upload passed");
  }

  async testWorkflowStart() {
    console.log("⚙️ Testing workflow start...");

    const request = {
      fabric_swatch: this.results.upload.fabric_swatch,
      scene_destination: this.results.upload.scene_destination,
      curtain_mask: this.results.upload.curtain_mask,
    };

    const response = await fetch(`${this.baseUrl}/api/inpaint/run`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Workflow start failed: ${error.detail}`);
    }

    this.results.workflow = await response.json();
    console.log("✅ Workflow start passed");
  }

  async testProgressTracking() {
    console.log("📊 Testing progress tracking...");

    return new Promise((resolve, reject) => {
      const promptId = this.results.workflow.prompt_id;
      const ws = new WebSocket(`ws://localhost:8000/ws/progress/${promptId}`);

      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error("Progress tracking timeout"));
      }, 30000);

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.results.progress.push(data);

        console.log("📈 Progress update:", data);

        // Check for completion
        if (data.type === "executing" && data.data?.node === null) {
          clearTimeout(timeout);
          ws.close();
          console.log("✅ Progress tracking completed");
          resolve();
        }
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        reject(new Error("WebSocket error during progress tracking"));
      };

      ws.onopen = () => {
        console.log("🔗 WebSocket connected for progress tracking");
      };
    });
  }

  async testResultDownload() {
    console.log("⬇️ Testing result download...");

    const promptId = this.results.workflow.prompt_id;
    const response = await fetch(
      `${this.baseUrl}/api/inpaint/result/${promptId}`
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Result download failed: ${error.detail}`);
    }

    const blob = await response.blob();
    this.results.result = {
      size: blob.size,
      type: blob.type,
      url: URL.createObjectURL(blob),
    };

    console.log("✅ Result download passed");
  }

  createMockFile(name) {
    // Create a minimal PNG file (1x1 pixel)
    const pngData = new Uint8Array([
      0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xde, 0x00, 0x00, 0x00,
      0x0c, 0x49, 0x44, 0x41, 0x54, 0x08, 0xd7, 0x63, 0xf8, 0x00, 0x00, 0x00,
      0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
    ]);

    return new File([pngData], name, { type: "image/png" });
  }

  generateReport() {
    console.log("\n📊 Test Results Summary:");
    console.log("========================");
    console.log("Health:", this.results.health ? "✅ PASS" : "❌ FAIL");
    console.log("Upload:", this.results.upload ? "✅ PASS" : "❌ FAIL");
    console.log("Workflow:", this.results.workflow ? "✅ PASS" : "❌ FAIL");
    console.log("Progress Events:", this.results.progress.length);
    console.log("Result:", this.results.result ? "✅ PASS" : "❌ FAIL");
    console.log("Errors:", this.results.errors.length);

    if (this.results.errors.length > 0) {
      console.log("\n❌ Errors:");
      this.results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
  }
}

// Run the complete test
const tester = new WorkflowTester();
tester
  .runCompleteTest()
  .then(() => tester.generateReport())
  .catch(() => tester.generateReport());
```

## Error Handling Demonstrations

### 1. Invalid File Type Test

```javascript
async function testInvalidFileType() {
  console.log("🧪 Testing invalid file type handling...");

  // Create a text file instead of image
  const textFile = new File(["not an image"], "test.txt", {
    type: "text/plain",
  });
  const validImage = new File([new Uint8Array(100)], "valid.png", {
    type: "image/png",
  });

  const formData = new FormData();
  formData.append("fabric_swatch", textFile); // Invalid
  formData.append("scene_destination", validImage);
  formData.append("curtain_mask", validImage);

  try {
    const response = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      console.log("✅ Expected error caught:", error.detail);
      return { success: true, error: error.detail };
    } else {
      console.log("❌ Expected error but request succeeded");
      return { success: false, error: "Should have failed" };
    }
  } catch (error) {
    console.log("✅ Network error caught:", error.message);
    return { success: true, error: error.message };
  }
}
```

### 2. Missing Files Test

```javascript
async function testMissingFiles() {
  console.log("🧪 Testing missing files handling...");

  const request = {
    fabric_swatch: "nonexistent_file.png",
    scene_destination: "another_missing_file.png",
    curtain_mask: "missing_mask.png",
  };

  try {
    const response = await fetch("/api/inpaint/run", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      console.log("✅ Expected 400 error:", error.detail);
      return { success: true, status: response.status, error: error.detail };
    } else {
      console.log("❌ Expected error but request succeeded");
      return { success: false, error: "Should have failed with 400" };
    }
  } catch (error) {
    console.log("✅ Network error caught:", error.message);
    return { success: true, error: error.message };
  }
}
```

### 3. Invalid Prompt ID Test

```javascript
async function testInvalidPromptId() {
  console.log("🧪 Testing invalid prompt ID handling...");

  const invalidPromptId = "invalid-prompt-id-12345";

  try {
    const response = await fetch(`/api/inpaint/result/${invalidPromptId}`);

    if (response.status === 404) {
      const error = await response.json();
      console.log("✅ Expected 404 error:", error.detail);
      return { success: true, status: 404, error: error.detail };
    } else if (response.ok) {
      console.log("❌ Expected 404 but got success");
      return { success: false, error: "Should have returned 404" };
    } else {
      const error = await response.json();
      console.log("✅ Got error response:", error.detail);
      return { success: true, status: response.status, error: error.detail };
    }
  } catch (error) {
    console.log("✅ Network error caught:", error.message);
    return { success: true, error: error.message };
  }
}
```

### 4. WebSocket Error Handling Test

```javascript
async function testWebSocketErrors() {
  console.log("🧪 Testing WebSocket error handling...");

  const invalidPromptId = "invalid-ws-prompt-id";

  return new Promise((resolve) => {
    const ws = new WebSocket(
      `ws://localhost:8000/ws/progress/${invalidPromptId}`
    );
    let errorCaught = false;

    const timeout = setTimeout(() => {
      if (!errorCaught) {
        ws.close();
        console.log("✅ WebSocket timeout handled correctly");
        resolve({ success: true, error: "Connection timeout" });
      }
    }, 5000);

    ws.onopen = () => {
      console.log("🔗 WebSocket connected (unexpected for invalid prompt)");
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === "error") {
        errorCaught = true;
        clearTimeout(timeout);
        ws.close();
        console.log("✅ WebSocket error message received:", data.message);
        resolve({ success: true, error: data.message });
      }
    };

    ws.onerror = (error) => {
      errorCaught = true;
      clearTimeout(timeout);
      console.log("✅ WebSocket error event triggered");
      resolve({ success: true, error: "WebSocket error event" });
    };

    ws.onclose = (event) => {
      if (!errorCaught) {
        clearTimeout(timeout);
        console.log("✅ WebSocket closed without error message");
        resolve({ success: true, error: "Connection closed" });
      }
    };
  });
}
```

## Performance Testing

### 1. Concurrent Upload Test

```javascript
async function testConcurrentUploads(concurrency = 3) {
  console.log(`🚀 Testing ${concurrency} concurrent uploads...`);

  const createUploadPromise = (index) => {
    return new Promise(async (resolve) => {
      const startTime = Date.now();

      try {
        const files = {
          fabric_swatch: new File([`fabric-${index}`], `fabric-${index}.png`, {
            type: "image/png",
          }),
          scene_destination: new File(
            [`scene-${index}`],
            `scene-${index}.png`,
            { type: "image/png" }
          ),
          curtain_mask: new File([`mask-${index}`], `mask-${index}.png`, {
            type: "image/png",
          }),
        };

        const formData = new FormData();
        Object.entries(files).forEach(([key, file]) => {
          formData.append(key, file);
        });

        const response = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        });

        const duration = Date.now() - startTime;

        if (response.ok) {
          const result = await response.json();
          resolve({
            index,
            success: true,
            duration,
            sessionId: result.session_id,
          });
        } else {
          const error = await response.json();
          resolve({
            index,
            success: false,
            duration,
            error: error.detail,
          });
        }
      } catch (error) {
        const duration = Date.now() - startTime;
        resolve({
          index,
          success: false,
          duration,
          error: error.message,
        });
      }
    });
  };

  const promises = Array.from({ length: concurrency }, (_, i) =>
    createUploadPromise(i)
  );
  const results = await Promise.all(promises);

  // Analyze results
  const successful = results.filter((r) => r.success);
  const failed = results.filter((r) => !r.success);
  const avgDuration =
    results.reduce((sum, r) => sum + r.duration, 0) / results.length;

  console.log(`📊 Concurrent upload results:`);
  console.log(`   Successful: ${successful.length}/${concurrency}`);
  console.log(`   Failed: ${failed.length}/${concurrency}`);
  console.log(`   Average duration: ${avgDuration.toFixed(2)}ms`);

  if (failed.length > 0) {
    console.log(
      `   Failures:`,
      failed.map((f) => f.error)
    );
  }

  return {
    total: concurrency,
    successful: successful.length,
    failed: failed.length,
    avgDuration,
    results,
  };
}
```

### 2. WebSocket Connection Stress Test

```javascript
async function testWebSocketStress(connections = 5) {
  console.log(`🔗 Testing ${connections} concurrent WebSocket connections...`);

  const createWebSocketPromise = (index) => {
    return new Promise((resolve) => {
      const promptId = `stress-test-${index}-${Date.now()}`;
      const ws = new WebSocket(`ws://localhost:8000/ws/progress/${promptId}`);
      const startTime = Date.now();
      let connected = false;

      const timeout = setTimeout(() => {
        if (!connected) {
          ws.close();
          resolve({
            index,
            success: false,
            duration: Date.now() - startTime,
            error: "Connection timeout",
          });
        }
      }, 10000);

      ws.onopen = () => {
        connected = true;
        clearTimeout(timeout);

        // Keep connection open for a short time
        setTimeout(() => {
          ws.close();
          resolve({
            index,
            success: true,
            duration: Date.now() - startTime,
            error: null,
          });
        }, 2000);
      };

      ws.onerror = () => {
        clearTimeout(timeout);
        resolve({
          index,
          success: false,
          duration: Date.now() - startTime,
          error: "Connection error",
        });
      };
    });
  };

  const promises = Array.from({ length: connections }, (_, i) =>
    createWebSocketPromise(i)
  );
  const results = await Promise.all(promises);

  const successful = results.filter((r) => r.success);
  const failed = results.filter((r) => !r.success);
  const avgDuration =
    results.reduce((sum, r) => sum + r.duration, 0) / results.length;

  console.log(`📊 WebSocket stress test results:`);
  console.log(`   Successful connections: ${successful.length}/${connections}`);
  console.log(`   Failed connections: ${failed.length}/${connections}`);
  console.log(`   Average connection time: ${avgDuration.toFixed(2)}ms`);

  return {
    total: connections,
    successful: successful.length,
    failed: failed.length,
    avgDuration,
    results,
  };
}
```

## Live API Explorer

### Interactive Test Suite

```html
<!DOCTYPE html>
<html>
  <head>
    <title>ComfyUI API Explorer</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }
      .test-section {
        border: 1px solid #ddd;
        margin: 20px 0;
        padding: 20px;
        border-radius: 8px;
      }
      .test-section h3 {
        margin-top: 0;
        color: #333;
      }
      .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      .test-button:hover {
        background: #0056b3;
      }
      .test-button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .result-area {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 15px;
        margin: 10px 0;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 300px;
        overflow-y: auto;
      }
      .success {
        color: #28a745;
      }
      .error {
        color: #dc3545;
      }
      .info {
        color: #17a2b8;
      }
      .progress-bar {
        width: 100%;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
      }
      .progress-fill {
        height: 100%;
        background: #28a745;
        transition: width 0.3s;
      }
      .file-input {
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <h1>🧪 ComfyUI API Explorer</h1>
    <p>Interactive testing interface for the ComfyUI Inpainting Backend API</p>

    <!-- Health Check Section -->
    <div class="test-section">
      <h3>🏥 Health Check</h3>
      <button class="test-button" onclick="testHealth()">
        Test Health Endpoint
      </button>
      <div id="health-result" class="result-area"></div>
    </div>

    <!-- File Upload Section -->
    <div class="test-section">
      <h3>📁 File Upload Test</h3>
      <div class="file-input">
        <label
          >Fabric Swatch: <input type="file" id="fabric-file" accept="image/*"
        /></label>
      </div>
      <div class="file-input">
        <label
          >Scene Destination:
          <input type="file" id="scene-file" accept="image/*"
        /></label>
      </div>
      <div class="file-input">
        <label
          >Curtain Mask: <input type="file" id="mask-file" accept="image/*"
        /></label>
      </div>
      <button class="test-button" onclick="testFileUpload()">
        Upload Files
      </button>
      <button class="test-button" onclick="testMockUpload()">
        Test with Mock Files
      </button>
      <div id="upload-result" class="result-area"></div>
    </div>

    <!-- Workflow Section -->
    <div class="test-section">
      <h3>⚙️ Workflow Execution</h3>
      <button class="test-button" onclick="testWorkflowStart()">
        Start Workflow
      </button>
      <button class="test-button" onclick="testFullWorkflow()">
        Full Workflow Test
      </button>
      <div class="progress-bar">
        <div
          id="workflow-progress"
          class="progress-fill"
          style="width: 0%"
        ></div>
      </div>
      <div id="workflow-result" class="result-area"></div>
    </div>

    <!-- WebSocket Section -->
    <div class="test-section">
      <h3>🔗 WebSocket Testing</h3>
      <button class="test-button" onclick="testWebSocketConnection()">
        Test Connection
      </button>
      <button class="test-button" onclick="testWebSocketStress()">
        Stress Test (5 connections)
      </button>
      <div id="websocket-result" class="result-area"></div>
    </div>

    <!-- Error Testing Section -->
    <div class="test-section">
      <h3>🧪 Error Handling Tests</h3>
      <button class="test-button" onclick="testInvalidFileType()">
        Invalid File Type
      </button>
      <button class="test-button" onclick="testMissingFiles()">
        Missing Files
      </button>
      <button class="test-button" onclick="testInvalidPromptId()">
        Invalid Prompt ID
      </button>
      <div id="error-result" class="result-area"></div>
    </div>

    <!-- Performance Section -->
    <div class="test-section">
      <h3>🚀 Performance Tests</h3>
      <button class="test-button" onclick="testConcurrentUploads()">
        Concurrent Uploads
      </button>
      <button class="test-button" onclick="runAllTests()">Run All Tests</button>
      <div id="performance-result" class="result-area"></div>
    </div>

    <script>
      // Global state
      let lastUploadResult = null;
      let lastWorkflowResult = null;

      // Utility functions
      function log(elementId, message, type = "info") {
        const element = document.getElementById(elementId);
        const timestamp = new Date().toLocaleTimeString();
        const className =
          type === "success" ? "success" : type === "error" ? "error" : "info";
        element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        element.scrollTop = element.scrollHeight;
      }

      function clearLog(elementId) {
        document.getElementById(elementId).innerHTML = "";
      }

      // Test functions
      async function testHealth() {
        clearLog("health-result");
        log("health-result", "🏥 Testing health endpoint...");

        try {
          const response = await fetch("/api/health");
          const data = await response.json();

          if (response.ok) {
            log(
              "health-result",
              `✅ Health check passed: ${JSON.stringify(data)}`,
              "success"
            );
          } else {
            log(
              "health-result",
              `❌ Health check failed: ${response.status}`,
              "error"
            );
          }
        } catch (error) {
          log("health-result", `❌ Network error: ${error.message}`, "error");
        }
      }

      function createMockFile(name, content = "mock image data") {
        return new File([content], name, { type: "image/png" });
      }

      async function testMockUpload() {
        clearLog("upload-result");
        log("upload-result", "📁 Testing upload with mock files...");

        const formData = new FormData();
        formData.append("fabric_swatch", createMockFile("fabric.png"));
        formData.append("scene_destination", createMockFile("scene.png"));
        formData.append("curtain_mask", createMockFile("mask.png"));

        try {
          const response = await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });

          const data = await response.json();

          if (response.ok) {
            lastUploadResult = data;
            log(
              "upload-result",
              `✅ Upload successful: ${JSON.stringify(data, null, 2)}`,
              "success"
            );
          } else {
            log("upload-result", `❌ Upload failed: ${data.detail}`, "error");
          }
        } catch (error) {
          log("upload-result", `❌ Network error: ${error.message}`, "error");
        }
      }

      async function testFileUpload() {
        clearLog("upload-result");

        const fabricFile = document.getElementById("fabric-file").files[0];
        const sceneFile = document.getElementById("scene-file").files[0];
        const maskFile = document.getElementById("mask-file").files[0];

        if (!fabricFile || !sceneFile || !maskFile) {
          log("upload-result", "❌ Please select all three files", "error");
          return;
        }

        log("upload-result", "📁 Uploading selected files...");

        const formData = new FormData();
        formData.append("fabric_swatch", fabricFile);
        formData.append("scene_destination", sceneFile);
        formData.append("curtain_mask", maskFile);

        try {
          const response = await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });

          const data = await response.json();

          if (response.ok) {
            lastUploadResult = data;
            log(
              "upload-result",
              `✅ Upload successful: ${JSON.stringify(data, null, 2)}`,
              "success"
            );
          } else {
            log("upload-result", `❌ Upload failed: ${data.detail}`, "error");
          }
        } catch (error) {
          log("upload-result", `❌ Network error: ${error.message}`, "error");
        }
      }

      // Add more test functions here...
      // (Additional functions would be included in a real implementation)
    </script>
  </body>
</html>
```

## Summary

This interactive documentation provides:

### 🧪 **Testing Capabilities**

- **Health checks** - Verify API connectivity
- **File upload testing** - Test with real or mock files
- **Workflow execution** - End-to-end workflow testing
- **WebSocket testing** - Real-time progress tracking
- **Error handling** - Validate error responses
- **Performance testing** - Concurrent operations and stress testing

### 📊 **Data Flow Understanding**

- **Request/response examples** - See actual API interactions
- **Progress tracking** - Understand WebSocket message flow
- **Error scenarios** - Learn from failure cases
- **Performance metrics** - Measure API responsiveness

### 🔧 **Interactive Features**

- **Live testing interface** - Browser-based API explorer
- **Real-time results** - Immediate feedback on tests
- **Mock data support** - Test without real files
- **Comprehensive logging** - Detailed test output

### 📚 **Learning Resources**

- **Complete examples** - Copy-paste ready code
- **Best practices** - Production-ready patterns
- **Troubleshooting** - Common issues and solutions
- **Framework integration** - React, Vue.js examples

Use these examples to:

1. **Validate your integration** - Ensure correct API usage
2. **Debug issues** - Identify and resolve problems
3. **Learn the API** - Understand expected behavior
4. **Test performance** - Measure API capabilities
5. **Develop confidently** - Build robust applications

For more detailed information, refer to:

- [API Endpoints Reference](./endpoints.md)
- [Integration Guide](./integration-guide.md)
- [WebSocket Guide](./websocket-guide.md)
