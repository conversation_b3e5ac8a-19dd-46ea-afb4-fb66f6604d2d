# Data Models Reference

## Overview

This document provides detailed specifications for all data models used in the ComfyUI Inpainting Backend API, including request schemas, response formats, and validation rules.

## Request Models

### RunRequest

Used for starting inpainting workflows with pre-uploaded files.

```typescript
interface RunRequest {
  fabric_swatch: string;
  scene_destination: string;
  curtain_mask: string;
  comfy_server?: string;
}
```

#### Fields

| Field | Type | Required | Validation | Description |
|-------|------|----------|------------|-------------|
| `fabric_swatch` | string | Yes | Filename only, no paths | Filename in input/ directory for fabric texture |
| `scene_destination` | string | Yes | Filename only, no paths | Filename in input/ directory for scene image |
| `curtain_mask` | string | Yes | Filename only, no paths | Filename in input/ directory for curtain mask |
| `comfy_server` | string | No | Valid host:port format | Override ComfyUI server address |

#### Example

```json
{
  "fabric_swatch": "fabric_swatch_a1b2c3d4.png",
  "scene_destination": "scene_destination_a1b2c3d4.png",
  "curtain_mask": "curtain_mask_a1b2c3d4.png",
  "comfy_server": "127.0.0.1:8188"
}
```

#### Validation Rules

- **Filename Security**: All filenames must be base names only (no directory paths)
- **File Existence**: Files must exist in the configured input directory
- **Server Format**: ComfyUI server must be in `host:port` format if provided

## Response Models

### RunResponse

Returned when a workflow is successfully queued.

```typescript
interface RunResponse {
  prompt_id: string;
  progress_ws: string;
  result_url: string;
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `prompt_id` | string | Unique identifier for workflow execution | `"abc123-def456-ghi789"` |
| `progress_ws` | string | WebSocket URL for real-time progress updates | `"/ws/progress/abc123-def456-ghi789"` |
| `result_url` | string | URL to fetch generated image when complete | `"/api/inpaint/result/abc123-def456-ghi789"` |

#### Example

```json
{
  "prompt_id": "abc123-def456-ghi789",
  "progress_ws": "/ws/progress/abc123-def456-ghi789",
  "result_url": "/api/inpaint/result/abc123-def456-ghi789"
}
```

### UploadResponse

Returned when files are successfully uploaded.

```typescript
interface UploadResponse {
  message: string;
  session_id: string;
  fabric_swatch: string;
  scene_destination: string;
  curtain_mask: string;
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `message` | string | Success message | `"Files uploaded successfully"` |
| `session_id` | string | Unique session identifier | `"a1b2c3d4"` |
| `fabric_swatch` | string | Generated filename for fabric swatch | `"fabric_swatch_a1b2c3d4.png"` |
| `scene_destination` | string | Generated filename for scene image | `"scene_destination_a1b2c3d4.png"` |
| `curtain_mask` | string | Generated filename for curtain mask | `"curtain_mask_a1b2c3d4.png"` |

#### Example

```json
{
  "message": "Files uploaded successfully",
  "session_id": "a1b2c3d4",
  "fabric_swatch": "fabric_swatch_a1b2c3d4.png",
  "scene_destination": "scene_destination_a1b2c3d4.png",
  "curtain_mask": "curtain_mask_a1b2c3d4.png"
}
```

### ErrorResponse

Standard error response format for all endpoints.

```typescript
interface ErrorResponse {
  detail: string;
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `detail` | string | Human-readable error message with specific details | `"Input file not found: fabric_swatch.png"` |

#### Example

```json
{
  "detail": "Input file not found: fabric_swatch.png"
}
```

### HealthResponse

Response from the health check endpoint.

```typescript
interface HealthResponse {
  status: string;
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `status` | string | API health status | `"ok"` |

#### Example

```json
{
  "status": "ok"
}
```

## WebSocket Message Models

### ProgressMessage

Sent during K-sampler steps and other progress-reporting operations.

```typescript
interface ProgressMessage {
  type: "progress";
  data: {
    value: number;
    max: number;
    prompt_id: string;
    node: string;
  };
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `type` | string | Message type identifier | `"progress"` |
| `data.value` | number | Current progress step | `5` |
| `data.max` | number | Total steps for this operation | `20` |
| `data.prompt_id` | string | Workflow identifier | `"abc123-def456-ghi789"` |
| `data.node` | string | ComfyUI node ID being executed | `"25"` |

#### Example

```json
{
  "type": "progress",
  "data": {
    "value": 5,
    "max": 20,
    "prompt_id": "abc123-def456-ghi789",
    "node": "25"
  }
}
```

### ExecutingMessage

Sent when workflow nodes start or finish execution.

```typescript
interface ExecutingMessage {
  type: "executing";
  data: {
    node: string | null;
    prompt_id: string;
  };
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `type` | string | Message type identifier | `"executing"` |
| `data.node` | string \| null | Node ID being executed, `null` indicates completion | `"25"` or `null` |
| `data.prompt_id` | string | Workflow identifier | `"abc123-def456-ghi789"` |

#### Examples

**Node Execution:**
```json
{
  "type": "executing",
  "data": {
    "node": "25",
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

**Workflow Completion:**
```json
{
  "type": "executing",
  "data": {
    "node": null,
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

### ExecutionCachedMessage

Sent when nodes are skipped due to caching.

```typescript
interface ExecutionCachedMessage {
  type: "execution_cached";
  data: {
    nodes: string[];
    prompt_id: string;
  };
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `type` | string | Message type identifier | `"execution_cached"` |
| `data.nodes` | string[] | Array of cached node IDs | `["18", "294"]` |
| `data.prompt_id` | string | Workflow identifier | `"abc123-def456-ghi789"` |

#### Example

```json
{
  "type": "execution_cached",
  "data": {
    "nodes": ["18", "294"],
    "prompt_id": "abc123-def456-ghi789"
  }
}
```

### ExecutedMessage

Sent when nodes complete execution with output information.

```typescript
interface ExecutedMessage {
  type: "executed";
  data: {
    node: string;
    prompt_id: string;
    output: {
      images?: Array<{
        filename: string;
        subfolder: string;
        type: string;
      }>;
    };
  };
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `type` | string | Message type identifier | `"executed"` |
| `data.node` | string | Completed node ID | `"25"` |
| `data.prompt_id` | string | Workflow identifier | `"abc123-def456-ghi789"` |
| `data.output.images` | array | Generated image metadata (if applicable) | See example below |

#### Example

```json
{
  "type": "executed",
  "data": {
    "node": "25",
    "prompt_id": "abc123-def456-ghi789",
    "output": {
      "images": [
        {
          "filename": "ComfyUI_00001_.png",
          "subfolder": "",
          "type": "output"
        }
      ]
    }
  }
}
```

### ErrorMessage

Sent when errors occur during processing.

```typescript
interface ErrorMessage {
  type: "error";
  message: string;
}
```

#### Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `type` | string | Message type identifier | `"error"` |
| `message` | string | Error description | `"Connection failed or processing error"` |

#### Example

```json
{
  "type": "error",
  "message": "Connection failed or processing error"
}
```

## File Upload Models

### Multipart Form Data

File uploads use `multipart/form-data` encoding with the following fields:

#### Upload Endpoint Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `fabric_swatch` | File | Yes | Fabric texture/pattern image |
| `scene_destination` | File | Yes | Room/window scene image |
| `curtain_mask` | File | Yes | Curtain mask image |

#### Run-with-Files Endpoint Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `fabric_swatch` | File | Yes | Fabric texture/pattern image |
| `scene_destination` | File | Yes | Room/window scene image |
| `curtain_mask` | File | Yes | Curtain mask image |
| `comfy_server` | string | No | Override ComfyUI server address |

#### File Validation

- **Supported Formats**: PNG, JPG, JPEG, WebP
- **Content-Type**: Must match file format
- **Size Limits**: No explicit limits (server dependent)
- **Naming**: Files are renamed with session-based prefixes

## TypeScript Definitions

Complete TypeScript definitions for all models:

```typescript
// Request Models
export interface RunRequest {
  fabric_swatch: string;
  scene_destination: string;
  curtain_mask: string;
  comfy_server?: string;
}

// Response Models
export interface RunResponse {
  prompt_id: string;
  progress_ws: string;
  result_url: string;
}

export interface UploadResponse {
  message: string;
  session_id: string;
  fabric_swatch: string;
  scene_destination: string;
  curtain_mask: string;
}

export interface ErrorResponse {
  detail: string;
}

export interface HealthResponse {
  status: string;
}

// WebSocket Message Models
export interface ProgressMessage {
  type: "progress";
  data: {
    value: number;
    max: number;
    prompt_id: string;
    node: string;
  };
}

export interface ExecutingMessage {
  type: "executing";
  data: {
    node: string | null;
    prompt_id: string;
  };
}

export interface ExecutionCachedMessage {
  type: "execution_cached";
  data: {
    nodes: string[];
    prompt_id: string;
  };
}

export interface ExecutedMessage {
  type: "executed";
  data: {
    node: string;
    prompt_id: string;
    output: {
      images?: Array<{
        filename: string;
        subfolder: string;
        type: string;
      }>;
    };
  };
}

export interface ErrorMessage {
  type: "error";
  message: string;
}

// Union type for all WebSocket messages
export type WebSocketMessage = 
  | ProgressMessage 
  | ExecutingMessage 
  | ExecutionCachedMessage 
  | ExecutedMessage 
  | ErrorMessage;
```

## Validation Examples

### Client-side Validation

```typescript
function validateRunRequest(request: RunRequest): string[] {
  const errors: string[] = [];

  // Check required fields
  if (!request.fabric_swatch) errors.push('fabric_swatch is required');
  if (!request.scene_destination) errors.push('scene_destination is required');
  if (!request.curtain_mask) errors.push('curtain_mask is required');

  // Validate filenames (no paths)
  const filenames = [request.fabric_swatch, request.scene_destination, request.curtain_mask];
  filenames.forEach(filename => {
    if (filename && filename.includes('/') || filename.includes('\\')) {
      errors.push(`Invalid filename: ${filename} (paths not allowed)`);
    }
  });

  // Validate server format if provided
  if (request.comfy_server) {
    const serverRegex = /^[a-zA-Z0-9.-]+:\d+$/;
    if (!serverRegex.test(request.comfy_server)) {
      errors.push('comfy_server must be in host:port format');
    }
  }

  return errors;
}
```

This comprehensive data model reference provides all the information needed to work with the ComfyUI Inpainting Backend API data structures.
