# ComfyUI Inpainting Backend API Documentation

## Overview

The ComfyUI Inpainting Backend API provides a comprehensive solution for AI-powered curtain fabric inpainting workflows. This API allows frontend applications to submit images for processing, track progress in real-time, and retrieve generated results.

## Quick Start

1. **Submit a workflow**: Use `/api/inpaint/run` or `/api/inpaint/run-with-files` to start processing
2. **Track progress**: Connect to `/ws/progress/{prompt_id}` for real-time updates
3. **Download results**: Fetch the generated image from `/api/inpaint/result/{prompt_id}`

## API Base Information

- **Base URL**: `http://localhost:8000` (configurable)
- **API Version**: 0.1.0
- **Content-Type**: `application/json` (except file uploads)
- **Authentication**: None required (CORS enabled for all origins)

## Available Documentation

### 📚 Core Documentation
- [**API Endpoints Reference**](./endpoints.md) - Complete endpoint specifications
- [**Integration Guide**](./integration-guide.md) - Step-by-step integration instructions
- [**WebSocket Guide**](./websocket-guide.md) - Real-time progress tracking
- [**Interactive Examples**](./examples.md) - Live examples and testing

### 🔧 Technical References
- [**Error Handling**](./error-handling.md) - Error codes and troubleshooting
- [**Data Models**](./data-models.md) - Request/response schemas
- [**Configuration**](./configuration.md) - Environment setup and configuration

## Key Features

### 🎨 Curtain Fabric Inpainting
- Upload fabric swatches, scene images, and curtain masks
- AI-powered fabric texture application to curtain areas
- High-quality image generation with customizable parameters

### ⚡ Real-time Progress Tracking
- WebSocket-based progress updates
- Live workflow execution monitoring
- Completion notifications and error handling

### 🔄 Flexible Workflow Options
- File-first approach: Upload files then start workflow
- All-in-one approach: Upload and process in single request
- Custom ComfyUI server configuration support

### 📁 File Management
- Automatic file validation and processing
- Secure file handling with session-based naming
- Support for PNG, JPG, JPEG, and WebP formats

## Architecture Overview

```
Frontend Application
        ↓
   API Gateway (FastAPI)
        ↓
   ComfyUI Integration
        ↓
   AI Processing Pipeline
        ↓
   Result Generation
```

## Workflow Process

1. **File Upload**: Submit fabric swatch, scene destination, and curtain mask images
2. **Validation**: API validates file formats and required parameters
3. **ComfyUI Integration**: Files are uploaded to ComfyUI server and workflow is queued
4. **Progress Tracking**: Real-time updates via WebSocket connection
5. **Result Retrieval**: Download generated image when processing completes

## Interactive API Documentation

For interactive testing and exploration:

- **Swagger UI**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **ReDoc**: [http://localhost:8000/redoc](http://localhost:8000/redoc)
- **Test Interface**: [http://localhost:8000/](http://localhost:8000/)

## Getting Started

### Prerequisites
- ComfyUI server running (default: `127.0.0.1:8188`)
- Input directory for file uploads (default: `./input`)
- Workflow JSON file (default: `./Subject Destination API.json`)

### Environment Configuration
```bash
# ComfyUI server address
COMFY_SERVER=127.0.0.1:8188

# File directories
INPUT_DIR=./input

# Workflow configuration
WORKFLOW_PATH=./Subject Destination API.json
RANDOMIZE_SEED=true

# Node IDs for workflow patching
NODE_SCENE_ID=18
NODE_FABRIC_ID=294
NODE_MASK_ID=303
NODE_OUTPUT_ID=302

# Progress tracking
PROGRESS_QUEUE_SIZE=200
```

### Health Check
Verify the API is running:
```bash
curl http://localhost:8000/api/health
```

Expected response:
```json
{
  "status": "ok"
}
```

## Support and Troubleshooting

- Check the [Error Handling Guide](./error-handling.md) for common issues
- Review [Configuration](./configuration.md) for setup problems
- Use the interactive documentation for testing endpoints
- Monitor WebSocket connections for real-time debugging

## Next Steps

1. Review the [API Endpoints Reference](./endpoints.md) for detailed specifications
2. Follow the [Integration Guide](./integration-guide.md) for implementation
3. Test with [Interactive Examples](./examples.md)
4. Implement [WebSocket progress tracking](./websocket-guide.md)
