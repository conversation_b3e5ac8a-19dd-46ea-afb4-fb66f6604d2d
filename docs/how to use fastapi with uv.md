Using uv with FastAPI
FastAPI is a modern, high-performance Python web framework. You can use uv to manage your FastAPI project, including installing dependencies, managing environments, running FastAPI applications, and more.

Note

You can view the source code for this guide in the uv-fastapi-example repository.

Migrating an existing FastAPI project
As an example, consider the sample application defined in the FastAPI documentation, structured as follows:


project
└── app
    ├── __init__.py
    ├── main.py
    ├── dependencies.py
    ├── routers
    │   ├── __init__.py
    │   ├── items.py
    │   └── users.py
    └── internal
        ├── __init__.py
        └── admin.py
To use uv with this application, inside the project directory run:


uv init --app
This creates a project with an application layout and a pyproject.toml file.

Then, add a dependency on FastAPI:


uv add fastapi --extra standard
You should now have the following structure:


project
├── pyproject.toml
└── app
    ├── __init__.py
    ├── main.py
    ├── dependencies.py
    ├── routers
    │   ├── __init__.py
    │   ├── items.py
    │   └── users.py
    └── internal
        ├── __init__.py
        └── admin.py
And the contents of the pyproject.toml file should look something like this:

pyproject.toml

[project]
name = "uv-fastapi-example"
version = "0.1.0"
description = "FastAPI project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi[standard]",
]
From there, you can run the FastAPI application with:


uv run fastapi dev
uv run will automatically resolve and lock the project dependencies (i.e., create a uv.lock alongside the pyproject.toml), create a virtual environment, and run the command in that environment.

Test the app by opening http://127.0.0.1:8000/?token=jessica in a web browser.