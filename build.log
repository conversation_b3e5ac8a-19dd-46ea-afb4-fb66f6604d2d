#0 building with "default" instance using docker driver

#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 8.65kB done
#1 DONE 0.0s

#2 [internal] load metadata for docker.io/library/python:3.13-slim
#2 ...

#3 [auth] library/python:pull token for registry-1.docker.io
#3 DONE 0.0s

#2 [internal] load metadata for docker.io/library/python:3.13-slim
#2 DONE 2.5s

#4 [internal] load .dockerignore
#4 transferring context: 1.45kB done
#4 DONE 0.0s

#5 [ 1/17] FROM docker.io/library/python:3.13-slim@sha256:58c30f5bfaa718b5803a53393190b9c68bd517c44c6c94c1b6c8c172bcfad040
#5 resolve docker.io/library/python:3.13-slim@sha256:58c30f5bfaa718b5803a53393190b9c68bd517c44c6c94c1b6c8c172bcfad040 0.0s done
#5 DONE 0.0s

#6 [internal] load build context
#6 transferring context: 753B done
#6 DONE 0.0s

#7 [ 3/17] RUN pip install --no-cache-dir --upgrade pip setuptools wheel
#7 CACHED

#8 [ 9/17] COPY static/ ./static/
#8 CACHED

#9 [14/17] RUN cat > /workspace/startup.sh << 'STARTUP_EOF' && chmod +x /workspace/startup.sh
#9 CACHED

#10 [ 8/17] COPY app/ ./app/
#10 CACHED

#11 [ 5/17] RUN uv --version
#11 CACHED

#12 [16/17] RUN echo "🔍 Verifying scripts..." &&     ls -la /workspace/*.sh &&     echo "📄 Script headers:" &&     head -3 /workspace/startup.sh &&     head -3 /workspace/setup-environment.sh
#12 CACHED

#13 [ 7/17] COPY pyproject.toml README.md ./
#13 CACHED

#14 [12/17] COPY comfy-requirements.txt custom-nodes-repos.txt model-downloads.txt ./
#14 CACHED

#15 [13/17] RUN cat > /workspace/setup-environment.sh << 'SETUP_EOF' && chmod +x /workspace/setup-environment.sh
#15 CACHED

#16 [15/17] RUN mkdir -p input output logs
#16 CACHED

#17 [ 4/17] RUN curl -LsSf https://astral.sh/uv/install.sh | sh
#17 CACHED

#18 [11/17] RUN uv sync --no-dev
#18 CACHED

#19 [ 2/17] RUN apt-get update && apt-get install -y --no-install-recommends     git curl wget unzip ffmpeg     libsm6 libxext6 libfontconfig1 libglib2.0-0 libgl1 libxrender1     && rm -rf /var/lib/apt/lists/*
#19 CACHED

#20 [10/17] COPY Subject_Destination_API.json ./
#20 CACHED

#21 [ 6/17] WORKDIR /workspace
#21 CACHED

#22 [17/17] RUN echo '#!/bin/bash' > /workspace/healthcheck.sh &&     echo 'curl -f http://localhost:8188/system_stats >/dev/null 2>&1 && \' >> /workspace/healthcheck.sh &&     echo 'curl -f http://localhost:8000/api/health >/dev/null 2>&1' >> /workspace/healthcheck.sh &&     chmod +x /workspace/healthcheck.sh
#22 CACHED

#23 exporting to image
#23 exporting layers done
#23 exporting manifest sha256:6c975fdfab76ac75438e5ab9af899d31477f566b66db312d800eb67f4b5a2c18 0.0s done
#23 exporting config sha256:b1a6b2aa2bb0a99d4c4ea8a0b51272ce9e37d7eccbf09c1dfd45dd289869f8d0
#23 exporting config sha256:b1a6b2aa2bb0a99d4c4ea8a0b51272ce9e37d7eccbf09c1dfd45dd289869f8d0 0.0s done
#23 exporting attestation manifest sha256:879be51f0d68300c1c3f93430d49341e329532d19c580b69c4c31d0763087089 0.0s done
#23 exporting manifest list sha256:434d59f23963ca4742e694780e7690f5d3f7d1388ce53bff1cad5cfc754631c8 0.0s done
#23 naming to docker.io/aruntd008/comfyui-fastapi:latest done
#23 unpacking to docker.io/aruntd008/comfyui-fastapi:latest 0.0s done
#23 DONE 0.2s
