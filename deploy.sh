#!/bin/bash
# RunPod Deployment Script for ComfyUI + FastAPI

set -e

# Configuration
DOCKER_USERNAME=${DOCKER_USERNAME:-"your-username"}
IMAGE_NAME="comfyui-fastapi"
TAG=${TAG:-"latest"}
FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${TAG}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    echo_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        echo_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        echo_error "Docker is not running or you don't have permission to use it"
        exit 1
    fi
    
    echo_success "Docker is available and running"
    
    # Check required files
    required_files=(
        "Dockerfile" 
        "app/main.py" 
        "Subject_Destination_API.json"
        "comfy-requirements.txt"
        "custom-nodes-repos.txt" 
        "model-downloads.txt"
        "setup-environment.sh"
        "startup.sh"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo_error "Required file not found: $file"
            exit 1
        fi
    done
    
    # Check script permissions
    script_files=("setup-environment.sh" "startup.sh")
    for script in "${script_files[@]}"; do
        if [ ! -x "$script" ]; then
            echo_warning "Making $script executable..."
            chmod +x "$script"
        fi
    done
    
    echo_success "All required files found and scripts are executable"
}

# Function to build Docker image
build_image() {
    echo_info "Building Docker image: ${FULL_IMAGE_NAME}"
    
    # Check if Dockerfile exists
    if [ ! -f "Dockerfile" ]; then
        echo_error "Dockerfile not found in current directory"
        exit 1
    fi
    
    # Build the image
    docker build \
        --tag ${FULL_IMAGE_NAME} \
        --progress=plain \
        .
    
    if [ $? -eq 0 ]; then
        echo_success "Docker image built successfully: ${FULL_IMAGE_NAME}"
    else
        echo_error "Failed to build Docker image"
        exit 1
    fi
}

# Function to test image locally
test_image() {
    echo_info "Testing Docker image locally..."
    
    # Stop any existing container
    docker stop comfyui-fastapi-test 2>/dev/null || true
    docker rm comfyui-fastapi-test 2>/dev/null || true
    
    # Run the container for testing
    echo_info "Starting container for testing..."
    docker run -d \
        --name comfyui-fastapi-test \
        --gpus all \
        -p 8000:8000 \
        -p 8188:8188 \
        ${FULL_IMAGE_NAME}
    
    # Wait for services to start
    echo_info "Waiting for services to start (this may take a few minutes)..."
    sleep 30
    
    # Test health endpoints
    for i in {1..10}; do
        if curl -f http://localhost:8000/api/health >/dev/null 2>&1; then
            echo_success "FastAPI health check passed"
            break
        fi
        if [ $i -eq 10 ]; then
            echo_error "FastAPI health check failed after 10 attempts"
            docker logs comfyui-fastapi-test
            docker stop comfyui-fastapi-test
            docker rm comfyui-fastapi-test
            exit 1
        fi
        echo_info "Health check attempt $i/10..."
        sleep 10
    done
    
    # Test ComfyUI
    for i in {1..10}; do
        if curl -f http://localhost:8188/system_stats >/dev/null 2>&1; then
            echo_success "ComfyUI health check passed"
            break
        fi
        if [ $i -eq 10 ]; then
            echo_warning "ComfyUI health check failed, but continuing..."
            break
        fi
        echo_info "ComfyUI check attempt $i/10..."
        sleep 10
    done
    
    # Cleanup test container
    docker stop comfyui-fastapi-test
    docker rm comfyui-fastapi-test
    
    echo_success "Local testing completed successfully"
}

# Function to push image to registry
push_image() {
    echo_info "Pushing Docker image to registry..."
    
    # Check if logged in to Docker Hub
    if ! docker info | grep -q "Username:"; then
        echo_warning "You may need to login to Docker Hub"
        echo_info "Run: docker login"
        read -p "Do you want to continue? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    docker push ${FULL_IMAGE_NAME}
    
    if [ $? -eq 0 ]; then
        echo_success "Docker image pushed successfully to registry"
        echo_info "Image URL: ${FULL_IMAGE_NAME}"
    else
        echo_error "Failed to push Docker image"
        exit 1
    fi
}

# Function to generate RunPod template
generate_runpod_template() {
    echo_info "Generating RunPod template..."
    
    cat > runpod-template.json << EOF
{
  "name": "ComfyUI FastAPI Backend",
  "readme": "# ComfyUI + FastAPI Backend\n\nThis template provides:\n- ComfyUI for AI image generation\n- FastAPI backend for curtain fabric inpainting\n- Real-time progress tracking via WebSocket\n- Web interface for testing\n\n## Ports:\n- 8000: FastAPI backend\n- 8188: ComfyUI interface\n\n## Usage:\n1. Wait for both services to start (check logs)\n2. Access web interface at: https://your-pod-id-8000.proxy.runpod.net\n3. Upload images and start inpainting workflow\n\n## API Documentation:\n- Swagger UI: https://your-pod-id-8000.proxy.runpod.net/docs\n- ReDoc: https://your-pod-id-8000.proxy.runpod.net/redoc\n\n## Cost Optimization:\n- Enable auto-sleep when idle\n- Pod will sleep after 5 minutes of inactivity\n- Wake up in ~30 seconds when accessed\n- Models stay loaded on disk (fast resume)\n\n## Resource Requirements:\n- GPU: RTX 3090/4090 or better (16GB+ VRAM)\n- Disk: 70GB+ for models and custom nodes\n- RAM: 16GB+ recommended",
  "imageName": "${FULL_IMAGE_NAME}",
  "dockerArgs": "",
  "ports": "8000/http,8188/http",
  "volumeMountPath": "/workspace",
  "volumeSize": 80,
  "env": [
    {
      "key": "COMFY_SERVER",
      "value": "127.0.0.1:8188"
    },
    {
      "key": "RANDOMIZE_SEED",
      "value": "true"
    },
    {
      "key": "PROGRESS_QUEUE_SIZE",
      "value": "200"
    }
  ],
  "startJupyter": false,
  "startSsh": true,
  "category": "AI/ML"
}
EOF
    
    echo_success "RunPod template generated: runpod-template.json"
    echo_info "You can use this template when creating your RunPod deployment"
}

# Function to show deployment instructions
show_deployment_instructions() {
    echo_info "Deployment Instructions:"
    echo
    echo "1. 📝 Copy the template:"
    echo "   Open runpod-template.json and copy its contents"
    echo
    echo "2. 🌐 Go to RunPod:"
    echo "   Visit: https://runpod.io"
    echo
    echo "3. 📋 Create Template:"
    echo "   - Go to 'My Templates'"
    echo "   - Click 'New Template'"
    echo "   - Paste the JSON content"
    echo "   - Save the template"
    echo
    echo "4. 🚀 Deploy Pod:"
    echo "   - Select your template"
    echo "   - Choose GPU (RTX 3090/4090/A100 recommended, min 16GB VRAM)"
    echo "   - Set disk space to at least 50GB (models are ~30GB total)"
    echo "   - Deploy the pod"
    echo
    echo "5. 🔗 Access Services:"
    echo "   - FastAPI: https://\${POD_ID}-8000.proxy.runpod.net"
    echo "   - ComfyUI: https://\${POD_ID}-8188.proxy.runpod.net"
    echo "   - API Docs: https://\${POD_ID}-8000.proxy.runpod.net/docs"
    echo
    echo "6. ⏱️  Wait for Startup:"
    echo "   - Initial startup takes 5-10 minutes (downloading ~30GB of models)"
    echo "   - Check logs for model download progress"
    echo "   - Wait for 'ComfyUI is ready!' and 'FastAPI started'"
    echo "   - Test health endpoint: /api/health"
    echo
    echo "📦 Models included (auto-downloaded):"
    echo "   - FLUX T5XXL & CLIP-L Text Encoders (~8GB)"
    echo "   - FLUX UNet Diffusion Model (~24GB)"
    echo "   - FLUX VAE Model (~500MB)"
    echo "   - SigLIP2 Vision Model (~1.5GB)"  
    echo "   - FLUX Redux Style Model (~12GB)"
    echo ""
    echo "🔧 Custom Nodes included:"
    echo "   - Your custom nodes: SVBRDF_node, ComfyUI_blender_render, comfyui_document_scanner"
    echo "   - Registry nodes: layerstyle, rgthree-comfy, easy-use, kjnodes, rmbg, etc."
    echo ""
    echo "🎯 Total container size: ~2GB (optimized!)"
    echo ""
    echo "⚡ What happens on RunPod:"
    echo "   - Downloads ~50GB models with fast network (5-10 min)"
    echo "   - Installs custom nodes from GitHub (2-3 min)"
    echo "   - First startup: 15-20 minutes total"
    echo "   - Subsequent starts: 30-60 seconds"
    echo
    echo_warning "Note: Replace \${POD_ID} with your actual RunPod pod ID"
    echo_warning "First startup downloads models - be patient!"
}

# Main execution
main() {
    echo_info "🚀 RunPod Deployment Script for ComfyUI + FastAPI"
    echo "======================================================"
    
    # Parse command line arguments
    SKIP_BUILD=false
    SKIP_TEST=false
    SKIP_PUSH=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-test)
                SKIP_TEST=true
                shift
                ;;
            --skip-push)
                SKIP_PUSH=true
                shift
                ;;
            --docker-username)
                DOCKER_USERNAME="$2"
                FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${TAG}"
                shift 2
                ;;
            --tag)
                TAG="$2"
                FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${TAG}"
                shift 2
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --skip-build        Skip Docker image build"
                echo "  --skip-test         Skip local testing"
                echo "  --skip-push         Skip pushing to registry"
                echo "  --docker-username   Set Docker Hub username"
                echo "  --tag              Set image tag (default: latest)"
                echo "  -h, --help         Show this help message"
                exit 0
                ;;
            *)
                echo_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_prerequisites
    
    # Build image
    if [ "$SKIP_BUILD" = false ]; then
        build_image
    else
        echo_warning "Skipping Docker image build"
    fi
    
    # Test image locally
    if [ "$SKIP_TEST" = false ]; then
        echo_info "Do you want to test the image locally? (y/n): "
        read -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            test_image
        else
            echo_warning "Skipping local testing"
        fi
    else
        echo_warning "Skipping local testing"
    fi
    
    # Push image to registry
    if [ "$SKIP_PUSH" = false ]; then
        push_image
    else
        echo_warning "Skipping push to registry"
    fi
    
    # Generate RunPod template
    generate_runpod_template
    
    # Show deployment instructions
    show_deployment_instructions
    
    echo_success "🎉 Deployment preparation completed!"
    echo_info "Next steps: Follow the deployment instructions above"
}

# Run main function
main "$@"