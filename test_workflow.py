#!/usr/bin/env python3
"""
Test script for the workflow executor
"""
import sys
from pathlib import Path

# Add the current directory to Python path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from workflow_executor import execute_inpaint, WorkflowResult


def custom_progress_callback(event):
    """Custom progress callback with emojis and colors"""
    event_type = event.get("type", "unknown")
    
    if event_type == "progress":
        data = event.get("data", {})
        value = data.get("value", 0)
        max_val = data.get("max", 100)
        node = data.get("node", "unknown")
        percentage = (value / max_val * 100) if max_val > 0 else 0
        
        # Create progress bar
        bar_length = 20
        filled_length = int(bar_length * value / max_val) if max_val > 0 else 0
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        print(f"🔄 [{bar}] {percentage:.1f}% - Node: {node}")
        
    elif event_type == "executing":
        data = event.get("data", {})
        node = data.get("node")
        if node:
            print(f"🚀 Executing node: {node}")
        else:
            print("🚀 Starting execution...")
            
    elif event_type == "execution_cached":
        data = event.get("data", {})
        nodes = data.get("nodes", [])
        print(f"⚡ Using cached results for {len(nodes)} nodes")
        
    elif event_type == "executed":
        data = event.get("data", {})
        node = data.get("node")
        print(f"✅ Completed node: {node}")
        
    elif event_type == "error":
        message = event.get("message", "Unknown error")
        print(f"💥 Error: {message}")


def main():
    print("🎨 ComfyUI Workflow Executor Test")
    print("=" * 50)
    
    # Check if input files exist
    input_files = ["ComfyUI_00169_.png", "scene_destination.png", "scene_mask.png"]
    input_dir = Path("input")
    
    for file in input_files:
        if not (input_dir / file).exists():
            print(f"❌ Missing input file: {file}")
            print(f"   Expected at: {input_dir / file}")
            return False
    
    print("✅ All input files found")
    print()
    
    # Execute workflow
    try:
        result = execute_inpaint(
            object_image="ComfyUI_00169_.png",
            scene_image="scene_destination.png", 
            scene_mask="scene_mask.png",
            progress_callback=custom_progress_callback,
            save_images=True,
            output_dir=Path("output")
        )
        
        print("\n" + "=" * 50)
        
        if result.success:
            print("🎉 WORKFLOW COMPLETED SUCCESSFULLY!")
            print(f"📋 Prompt ID: {result.prompt_id}")
            print(f"⏱️  Total execution time: {result.execution_time:.2f} seconds")
            print(f"📊 Progress events: {len(result.progress_events)}")
            print(f"🖼️  Generated images: {len(result.output_images)}")
            
            if result.output_images:
                print("\n📁 Output files:")
                for i, img in enumerate(result.output_images, 1):
                    local_path = img.get("local_path", "Not saved")
                    size_bytes = img.get("size_bytes", 0)
                    size_mb = size_bytes / (1024 * 1024) if size_bytes > 0 else 0
                    print(f"   {i}. {local_path} ({size_mb:.2f} MB)")
            
            return True
            
        else:
            print("❌ WORKFLOW FAILED!")
            print(f"💥 Error: {result.error_message}")
            print(f"⏱️  Failed after: {result.execution_time:.2f} seconds")
            print(f"📊 Progress events: {len(result.progress_events)}")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️  Workflow interrupted by user")
        return False
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)