from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Request, UploadFile, File, Form
from fastapi.responses import Response, JSONResponse
from pathlib import Path
from typing import Dict, Optional
import asyncio
import uuid
import shutil

from app.config import get_settings
from app.core.progress import ProgressBus
from app.models.schemas import RunRequest, RunResponse
from app.services.comfy_client import ComfyClient


router = APIRouter(prefix="/api")


# These will be set from app.state in main.py
def get_bus(request: Request) -> ProgressBus:
    return request.app.state.progress_bus  # type: ignore


def get_run_index(request: Request) -> Dict[str, str]:
    return request.app.state.run_index  # type: ignore


@router.get("/health", tags=["Health"])
async def health() -> Dict[str, str]:
    """Health check endpoint to verify the API is running"""
    return {"status": "ok"}


@router.post("/upload", tags=["File Upload"])
async def upload_images(
    fabric_swatch: UploadFile = File(..., description="Fabric texture/pattern image"),
    scene_destination: UploadFile = File(..., description="Room/window scene image"), 
    curtain_mask: UploadFile = File(..., description="Curtain mask image"),
) -> Dict[str, str]:
    """
    Upload images for curtain fabric workflow
    
    Accepts three image files and saves them to the input directory
    with standardized names for the workflow.
    
    Returns the filenames that can be used with the /inpaint/run endpoint.
    """
    settings = get_settings()
    
    # Generate unique session ID to avoid conflicts
    session_id = str(uuid.uuid4())[:8]
    
    # Validate file types
    allowed_types = {"image/jpeg", "image/jpg", "image/png", "image/webp"}
    files = {
        "fabric_swatch": fabric_swatch,
        "scene_destination": scene_destination, 
        "curtain_mask": curtain_mask
    }
    
    for name, file in files.items():
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400, 
                detail=f"{name} must be an image file (PNG, JPG, JPEG, or WebP)"
            )
    
    # Save files with standardized names
    saved_files = {}
    try:
        input_dir = settings.INPUT_DIR
        input_dir.mkdir(exist_ok=True)
        
        for file_type, upload_file in files.items():
            # Use session ID to make filenames unique
            filename = f"{file_type}_{session_id}.png"
            file_path = input_dir / filename
            
            # Save uploaded file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(upload_file.file, buffer)
                
            saved_files[file_type] = filename
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save uploaded files: {e}")
    
    return {
        "message": "Files uploaded successfully",
        "session_id": session_id,
        **saved_files
    }


@router.post("/inpaint/run-with-files", tags=["Inpainting"])
async def start_inpaint_with_files(
    request: Request,
    fabric_swatch: UploadFile = File(..., description="Fabric texture/pattern image"),
    scene_destination: UploadFile = File(..., description="Room/window scene image"), 
    curtain_mask: UploadFile = File(..., description="Curtain mask image"),
    comfy_server: Optional[str] = Form(None, description="Override ComfyUI server address")
) -> RunResponse:
    """
    Upload images and start curtain fabric workflow in one step
    
    This endpoint combines file upload and workflow execution:
    1. Uploads the three required images
    2. Starts the curtain fabric generation workflow
    3. Returns tracking URLs for progress and results
    """
    # First upload the files
    upload_result = await upload_images(fabric_swatch, scene_destination, curtain_mask)
    
    # Create a RunRequest with the uploaded filenames
    run_request = RunRequest(
        fabric_swatch=upload_result["fabric_swatch"],
        scene_destination=upload_result["scene_destination"], 
        curtain_mask=upload_result["curtain_mask"],
        comfy_server=comfy_server
    )
    
    # Use the existing workflow logic
    return await start_inpaint(run_request, request)


@router.post("/inpaint/run", response_model=RunResponse, tags=["Inpainting"])
async def start_inpaint(req: RunRequest, request: Request) -> RunResponse:
    """
    Start a curtain fabric inpainting workflow
    
    This endpoint:
    1. Validates and uploads your input images to ComfyUI
    2. Submits the curtain fabric inpainting workflow for execution  
    3. Returns tracking URLs for progress and results
    
    **Required files must exist in your input/ directory:**
    - Fabric swatch image (texture/pattern to apply)
    - Scene destination image (room/window scene)
    - Curtain mask (defines curtain areas)
    """
    settings = get_settings()

    # Sanitize filenames: only allow base names
    for name in (req.fabric_swatch, req.scene_destination, req.curtain_mask):
        if Path(name).name != name:
            raise HTTPException(status_code=400, detail="Filenames must not contain directories")

    # Resolve files under input dir
    input_dir = settings.INPUT_DIR
    fabric_path = (input_dir / req.fabric_swatch)
    scene_path = (input_dir / req.scene_destination)
    mask_path = (input_dir / req.curtain_mask)
    for p in (fabric_path, scene_path, mask_path):
        if not p.exists():
            raise HTTPException(status_code=400, detail=f"Input file not found: {p.name}")

    server_addr = req.comfy_server or settings.COMFY_SERVER
    client = ComfyClient(server_addr)

    # Upload files
    try:
        client.upload_file(fabric_path, req.fabric_swatch, image_type="image")
        client.upload_file(scene_path, req.scene_destination, image_type="image")
        client.upload_file(mask_path, req.curtain_mask, image_type="image")
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Failed to upload inputs to ComfyUI: {e}")

    # Load and patch workflow
    try:
        prompt = client.load_workflow(settings.WORKFLOW_PATH)
        prompt = client.patch_workflow(
            prompt,
            scene_filename=req.scene_destination,
            fabric_filename=req.fabric_swatch,
            mask_filename=req.curtain_mask,
            node_scene=settings.NODE_SCENE_ID,
            node_fabric=settings.NODE_FABRIC_ID,
            node_mask=settings.NODE_MASK_ID,
            randomize_seed=settings.RANDOMIZE_SEED,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to patch workflow: {e}")

    # Connect WS and queue prompt
    try:
        ws, client_id = client.connect_ws()
        prompt_id = client.queue_prompt(prompt, client_id)
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Failed to queue prompt or connect WS: {e}")

    # Register server address for this run (so result endpoint knows where to fetch)
    get_run_index(request)[prompt_id] = server_addr

    # Start background task pumping progress into bus
    bus = get_bus(request)

    async def pump() -> None:
        try:
            async def publish_event(evt: dict) -> None:
                await bus.publish(prompt_id, evt)
            
            await client.track_progress(ws, prompt_id, publish_event)
        finally:
            await bus.close(prompt_id)

    asyncio.create_task(pump())

    # Build response
    progress_ws = f"/ws/progress/{prompt_id}"
    result_url = f"/api/inpaint/result/{prompt_id}"
    return RunResponse(prompt_id=prompt_id, progress_ws=progress_ws, result_url=result_url)


@router.get("/inpaint/result/{prompt_id}", tags=["Inpainting"], 
          responses={
              200: {"content": {"image/png": {}}, "description": "Generated inpainted image"},
              404: {"description": "No outputs found for this prompt"},
              502: {"description": "Failed to fetch result from ComfyUI"}
          })
async def get_result(prompt_id: str, request: Request) -> Response:
    """
    Download the generated inpainting result
    
    Returns the PNG image file generated by the inpainting workflow.
    Call this endpoint after the workflow completes (monitor via WebSocket).
    """
    settings = get_settings()
    server_addr = get_run_index(request).get(prompt_id, settings.COMFY_SERVER)
    client = ComfyClient(server_addr)

    try:
        images = client.get_outputs_for_node(prompt_id, settings.NODE_OUTPUT_ID)
        if not images:
            raise HTTPException(status_code=404, detail="No outputs found for this prompt")
        img_meta = images[0]
        content = client.get_image(
            filename=img_meta["filename"],
            subfolder=img_meta.get("subfolder", ""),
            folder_type=img_meta.get("type", "output"),
        )
        return Response(content=content, media_type="image/png")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=502, detail=f"Failed to fetch result: {e}")


