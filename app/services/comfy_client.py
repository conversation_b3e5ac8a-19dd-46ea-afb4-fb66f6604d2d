import json
import uuid
import requests
import websocket  # websocket-client
import random
import time
from pathlib import Path
from typing import Callable, Awaitable, Dict, List, Optional
import asyncio


class ComfyClient:
    def __init__(self, server_address: str) -> None:
        # server_address like '127.0.0.1:8188'
        self.server_address = server_address

    def _http(self, path: str) -> str:
        return f"http://{self.server_address}{path}"

    def _ws(self, path: str) -> str:
        return f"ws://{self.server_address}{path}"

    def connect_ws(self) -> tuple[websocket.WebSocket, str]:
        client_id = str(uuid.uuid4())
        ws = websocket.WebSocket()
        ws.connect(self._ws(f"/ws?clientId={client_id}"))
        return ws, client_id

    def upload_file(
        self,
        input_path: Path,
        filename: str,
        image_type: str,  # 'image' or 'mask'
        folder_type: str = "input",
        overwrite: bool = False,
    ) -> bytes:
        with open(input_path, "rb") as f:
            files = {"image": (filename, f, "image/png")}
            data = {"type": folder_type, "overwrite": str(overwrite).lower()}
            url = self._http(f"/upload/{image_type}")
            resp = requests.post(url, files=files, data=data, timeout=60)
            resp.raise_for_status()
            return resp.content

    def load_workflow(self, workflow_path: Path) -> Dict:
        with open(workflow_path, "r", encoding="utf-8") as f:
            return json.load(f)

    def patch_workflow(self, prompt: Dict, *, scene_filename: str, fabric_filename: str, mask_filename: str,
                       node_scene: str, node_fabric: str, node_mask: str, randomize_seed: bool = True) -> Dict:
        # Only update the three specific nodes/fields
        if node_scene not in prompt or node_fabric not in prompt or node_mask not in prompt:
            raise ValueError("One or more required node IDs not found in workflow JSON")
        prompt[node_scene]["inputs"]["image"] = scene_filename
        prompt[node_fabric]["inputs"]["image"] = fabric_filename
        prompt[node_mask]["inputs"]["image"] = mask_filename
        
        # Randomize seed if requested (default: True)
        if randomize_seed:
            # Find RandomNoise nodes and update their seeds
            for node_id, node_data in prompt.items():
                if node_data.get("class_type") == "RandomNoise" and "inputs" in node_data:
                    # Generate random seed (same range as ComfyUI uses)
                    new_seed = random.randint(0, 2**64 - 1)
                    node_data["inputs"]["noise_seed"] = new_seed
                    print(f"🎲 Randomized seed for node {node_id}: {new_seed}")
            
            # Also add timestamp to SaveImage filename_prefix to force cache invalidation
            timestamp = int(time.time() * 1000)  # milliseconds for uniqueness
            for node_id, node_data in prompt.items():
                if node_data.get("class_type") == "SaveImage" and "inputs" in node_data:
                    current_prefix = node_data["inputs"].get("filename_prefix", "ComfyUI")
                    node_data["inputs"]["filename_prefix"] = f"{current_prefix}_{timestamp}"
                    print(f"🕒 Updated filename prefix for node {node_id}: {current_prefix}_{timestamp}")
        
        return prompt

    def queue_prompt(self, prompt: Dict, client_id: str) -> str:
        data = {"prompt": prompt, "client_id": client_id}
        headers = {"Content-Type": "application/json"}
        resp = requests.post(self._http("/prompt"), json=data, headers=headers, timeout=60)
        resp.raise_for_status()
        out = resp.json()
        prompt_id = out.get("prompt_id")
        if not prompt_id:
            raise RuntimeError("ComfyUI did not return a prompt_id")
        return prompt_id

    def _track_progress_blocking(self, ws: websocket.WebSocket, prompt_id: str, on_event: Callable[[dict], None]) -> None:
        try:
            while True:
                raw = ws.recv()
                msg = json.loads(raw)
                # Forward meaningful events
                typ = msg.get("type")
                if typ in {"progress", "executing", "execution_cached", "executed"}:
                    on_event(msg)
                # Completion detection: workflow finishes when we get executing with node=null
                data = msg.get("data", {})
                if typ == "executing" and data.get("node") is None and data.get("prompt_id") == prompt_id:
                    break
        except Exception as e:
            on_event({"type": "error", "message": str(e)})
        finally:
            try:
                ws.close()
            except Exception:
                pass

    async def track_progress(self, ws: websocket.WebSocket, prompt_id: str, on_event_async: Callable[[dict], Awaitable[None]]) -> None:
        loop = asyncio.get_running_loop()

        def sync_emit(evt: dict) -> None:
            # schedule deliver on loop thread-safely
            asyncio.run_coroutine_threadsafe(on_event_async(evt), loop)

        await asyncio.to_thread(self._track_progress_blocking, ws, prompt_id, sync_emit)

    def get_history(self, prompt_id: str) -> Dict:
        resp = requests.get(self._http(f"/history/{prompt_id}"), timeout=60)
        resp.raise_for_status()
        return resp.json()

    def get_image(self, filename: str, subfolder: str, folder_type: str) -> bytes:
        params = {"filename": filename, "subfolder": subfolder, "type": folder_type}
        resp = requests.get(self._http("/view"), params=params, timeout=120)
        resp.raise_for_status()
        return resp.content

    def get_outputs_for_node(self, prompt_id: str, node_id: str) -> List[dict]:
        history = self.get_history(prompt_id)
        outputs = history.get(prompt_id, {}).get("outputs", {})
        node_out = outputs.get(node_id)
        if not node_out:
            return []
        return node_out.get("images", [])

