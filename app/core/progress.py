import asyncio
from typing import Dict, List


def extract_progress_percentage(event: dict) -> float | None:
    """Extract progress percentage from ComfyUI progress event.
    Returns value between 0.0 and 1.0, or None if not a progress event.
    """
    if event.get("type") != "progress":
        return None
    data = event.get("data", {})
    value = data.get("value", 0)
    max_val = data.get("max", 1)
    if max_val == 0:
        return 0.0
    return min(1.0, max(0.0, value / max_val))


class ProgressBus:
    """In-memory broadcast bus mapping prompt_id -> list of queues.
    Each subscriber gets its own asyncio.Queue receiving all events for that prompt.
    """

    def __init__(self, max_queue_size: int = 200) -> None:
        self._subscribers: Dict[str, List[asyncio.Queue]] = {}
        self._lock = asyncio.Lock()
        self._max_queue_size = max_queue_size

    async def subscribe(self, prompt_id: str) -> asyncio.Queue:
        queue: asyncio.Queue = asyncio.Queue(maxsize=self._max_queue_size)
        async with self._lock:
            self._subscribers.setdefault(prompt_id, []).append(queue)
        return queue

    async def unsubscribe(self, prompt_id: str, queue: asyncio.Queue) -> None:
        async with self._lock:
            if prompt_id in self._subscribers:
                lst = self._subscribers[prompt_id]
                if queue in lst:
                    lst.remove(queue)
                if not lst:
                    self._subscribers.pop(prompt_id, None)

    async def publish(self, prompt_id: str, event: dict) -> None:
        async with self._lock:
            queues = list(self._subscribers.get(prompt_id, []))
        # push outside the lock to avoid blocking other ops
        for q in queues:
            try:
                q.put_nowait(event)
            except asyncio.QueueFull:
                # drop oldest by getting one and then put
                try:
                    _ = q.get_nowait()
                except Exception:
                    pass
                try:
                    q.put_nowait(event)
                except Exception:
                    pass

    async def close(self, prompt_id: str) -> None:
        # Optionally send a terminal event before cleanup
        await self.publish(prompt_id, {"type": "completed"})
        async with self._lock:
            self._subscribers.pop(prompt_id, None)

