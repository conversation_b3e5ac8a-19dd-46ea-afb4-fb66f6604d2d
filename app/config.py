import os
from pathlib import Path
from typing import Optional

try:
    from dotenv import load_dotenv  # type: ignore
    load_dotenv()
except Exception:
    # dotenv is optional; proceed if not installed
    pass


class Settings:
    def __init__(self) -> None:
        # ComfyUI server address (host:port) without protocol
        self.COMFY_SERVER: str = os.getenv("COMFY_SERVER", "127.0.0.1:8188")
        # Paths relative to project root by default
        self.INPUT_DIR: Path = Path(os.getenv("INPUT_DIR", "./input")).resolve()
        self.WORKFLOW_PATH: Path = Path(os.getenv("WORKFLOW_PATH", "./Subject_Destination_API.json")).resolve()
        # Progress buffering
        self.PROGRESS_QUEUE_SIZE: int = int(os.getenv("PROGRESS_QUEUE_SIZE", "200"))
        # Seed randomization
        self.RANDOMIZE_SEED: bool = os.getenv("RANDOMIZE_SEED", "true").lower() in ("true", "1", "yes")
        # Node IDs - Updated for new workflow
        self.NODE_SCENE_ID: str = os.getenv("NODE_SCENE_ID", "18")  # Load destination (input)
        self.NODE_FABRIC_ID: str = os.getenv("NODE_FABRIC_ID", "294")  # Load Fabric Swatch (input)  
        self.NODE_MASK_ID: str = os.getenv("NODE_MASK_ID", "303")  # Load curtain mask (input)
        self.NODE_OUTPUT_ID: str = os.getenv("NODE_OUTPUT_ID", "302")  # Save final result Image (output)


_settings: Optional[Settings] = None


def get_settings() -> Settings:
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings

