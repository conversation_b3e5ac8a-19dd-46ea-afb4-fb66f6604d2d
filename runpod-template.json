{"name": "ComfyUI FastAPI Backend", "readme": "# ComfyUI + FastAPI Backend\n\nThis template provides:\n- ComfyUI for AI image generation\n- FastAPI backend for curtain fabric inpainting\n- Real-time progress tracking via WebSocket\n- Web interface for testing\n\n## Ports:\n- 8000: FastAPI backend\n- 8188: ComfyUI interface\n\n## Usage:\n1. Wait for both services to start (check logs)\n2. Access web interface at: https://your-pod-id-8000.proxy.runpod.net\n3. Upload images and start inpainting workflow\n\n## API Documentation:\n- Swagger UI: https://your-pod-id-8000.proxy.runpod.net/docs\n- ReDoc: https://your-pod-id-8000.proxy.runpod.net/redoc\n\n## Cost Optimization:\n- Enable auto-sleep when idle\n- Pod will sleep after 5 minutes of inactivity\n- Wake up in ~30 seconds when accessed\n- Models stay loaded on disk (fast resume)\n\n## Resource Requirements:\n- GPU: RTX 3090/4090 or better (16GB+ VRAM)\n- Disk: 70GB+ for models and custom nodes\n- RAM: 16GB+ recommended", "imageName": "aruntd008/comfyui-fastapi:latest", "dockerArgs": "", "ports": "8000/http,8188/http", "volumeMountPath": "/workspace", "volumeSize": 80, "env": [{"key": "COMFY_SERVER", "value": "127.0.0.1:8188"}, {"key": "RANDOMIZE_SEED", "value": "true"}, {"key": "PROGRESS_QUEUE_SIZE", "value": "200"}], "startJupyter": false, "startSsh": true, "category": "AI/ML"}