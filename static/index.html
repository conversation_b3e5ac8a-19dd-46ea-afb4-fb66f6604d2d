<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComfyUI Curtain Fabric Backend - Test Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .section {
            margin-bottom: 30px;
        }

        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }

        h2 {
            color: #555;
            margin-top: 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        input[type="text"],
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #007acc;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        button:hover {
            background-color: #005a9a;
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            min-height: 50px;
        }

        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .progress-bar {
            width: 100%;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 20px;
            background-color: #007acc;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .endpoint-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .endpoint {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #007acc;
        }

        .method {
            font-weight: bold;
            margin-right: 10px;
            padding: 2px 8px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
        }

        .method.GET {
            background-color: #28a745;
        }

        .method.POST {
            background-color: #007bff;
        }

        .method.WS {
            background-color: #6f42c1;
        }

        .logs {
            background-color: #1e1e1e;
            color: #ffffff;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }

        /* Tab Styles */
        .tab-button {
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
            padding: 8px 16px;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            font-size: 14px;
        }

        .tab-button.active {
            background-color: #007acc;
            color: white;
            border-bottom: 1px solid #007acc;
        }

        .tab-button:hover:not(.active) {
            background-color: #e9ecef;
        }

        .tab-content {
            border: 1px solid #dee2e6;
            border-radius: 0 4px 4px 4px;
            padding: 20px;
            background-color: white;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>� ComfyUI Curtain Fabric Backend - Test Interface</h1>

        <div class="section">
            <h2>📋 Available Endpoints</h2>
            <div class="endpoint-list">
                <div class="endpoint">
                    <span class="method GET">GET</span>
                    <code>/api/health</code> - Health check
                </div>
                <div class="endpoint">
                    <span class="method POST">POST</span>
                    <code>/api/inpaint/run</code> - Start curtain fabric workflow (with existing files)
                </div>
                <div class="endpoint">
                    <span class="method POST">POST</span>
                    <code>/api/inpaint/upload-images</code> - Upload images for workflow
                </div>
                <div class="endpoint">
                    <span class="method POST">POST</span>
                    <code>/api/inpaint/upload-and-run</code> - Upload images and start workflow
                </div>
                <div class="endpoint">
                    <span class="method GET">GET</span>
                    <code>/api/inpaint/result/{prompt_id}</code> - Download result image
                </div>
                <div class="endpoint">
                    <span class="method WS">WS</span>
                    <code>/ws/progress/{prompt_id}</code> - Real-time progress updates
                </div>
                <div class="endpoint">
                    <span class="method GET">GET</span>
                    <code>/docs</code> - Swagger UI (Interactive API docs)
                </div>
                <div class="endpoint">
                    <span class="method GET">GET</span>
                    <code>/redoc</code> - ReDoc (Alternative API docs)
                </div>
            </div>
        </div>

        <div class="two-column">
            <div class="section">
                <h2>🎨 Test Curtain Fabric Workflow</h2>

                <!-- Tab Navigation -->
                <div style="margin-bottom: 20px;">
                    <button onclick="showTab('filename')" id="filenameTab" class="tab-button active">📁 Use Existing
                        Files</button>
                    <button onclick="showTab('upload')" id="uploadTab" class="tab-button">📤 Upload New Files</button>
                </div>

                <!-- Filename Form -->
                <div id="filenameForm" class="tab-content">
                    <form id="inpaintForm">
                        <div class="form-group">
                            <label for="fabricSwatch">Fabric Swatch Filename:</label>
                            <input type="text" id="fabricSwatch" value="fabric_swatch.png"
                                placeholder="e.g., fabric_swatch.png">
                            <small>Fabric texture/pattern image in your input/ directory</small>
                        </div>

                        <div class="form-group">
                            <label for="sceneDestination">Scene Destination Filename:</label>
                            <input type="text" id="sceneDestination" value="scene_destination.png"
                                placeholder="e.g., scene_destination.png">
                            <small>Room/window scene image</small>
                        </div>

                        <div class="form-group">
                            <label for="curtainMask">Curtain Mask Filename:</label>
                            <input type="text" id="curtainMask" value="curtain_mask.png"
                                placeholder="e.g., curtain_mask.png">
                            <small>Mask defining curtain areas</small>
                        </div>

                        <div class="form-group">
                            <label for="comfyServer">ComfyUI Server (Optional):</label>
                            <input type="text" id="comfyServer" placeholder="127.0.0.1:8188">
                        </div>

                        <button type="submit" id="submitBtn">🎨 Start Curtain Generation</button>
                    </form>
                </div>

                <!-- Upload Form -->
                <div id="uploadForm" class="tab-content" style="display: none;">
                    <form id="uploadInpaintForm" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="fabricSwatchFile">Fabric Swatch Image:</label>
                            <input type="file" id="fabricSwatchFile" accept="image/*" required>
                            <small>Upload fabric texture/pattern image (PNG, JPG, JPEG, WebP)</small>
                        </div>

                        <div class="form-group">
                            <label for="sceneDestinationFile">Scene Destination Image:</label>
                            <input type="file" id="sceneDestinationFile" accept="image/*" required>
                            <small>Upload room/window scene image</small>
                        </div>

                        <div class="form-group">
                            <label for="curtainMaskFile">Curtain Mask Image:</label>
                            <input type="file" id="curtainMaskFile" accept="image/*" required>
                            <small>Upload mask defining curtain areas</small>
                        </div>

                        <div class="form-group">
                            <label for="comfyServerUpload">ComfyUI Server (Optional):</label>
                            <input type="text" id="comfyServerUpload" placeholder="127.0.0.1:8188">
                        </div>

                        <button type="submit" id="uploadSubmitBtn">📤 Upload & Generate</button>
                    </form>
                </div>

                <div id="result" class="result" style="display: none;"></div>

                <div id="progressContainer" style="display: none;">
                    <h3>📊 Progress</h3>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill" style="width: 0%">0%</div>
                    </div>
                    <div id="progressLog" class="logs"></div>
                </div>
            </div>

            <div class="section">
                <h2>🔗 Quick Actions</h2>
                <button onclick="testHealth()" style="margin-bottom: 10px;">🏥 Test Health Endpoint</button><br>
                <button onclick="openSwagger()" style="margin-bottom: 10px;">📖 Open Swagger UI</button><br>
                <button onclick="openRedoc()" style="margin-bottom: 10px;">📚 Open ReDoc</button><br>

                <div id="healthResult" style="display: none; margin-top: 15px;"></div>

                <h3>📁 Current Input Files</h3>
                <div id="fileList">
                    <button onclick="listInputFiles()">🔍 List Input Directory</button>
                    <div id="files" style="margin-top: 10px;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPromptId = null;
        let progressSocket = null;

        // Test health endpoint
        async function testHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                showResult('healthResult', data, 'success');
            } catch (error) {
                showResult('healthResult', `Error: ${error.message}`, 'error');
            }
        }

        // Open Swagger UI
        function openSwagger() {
            window.open('/docs', '_blank');
        }

        // Open ReDoc
        function openRedoc() {
            window.open('/redoc', '_blank');
        }

        // List input files (mock - you'd need to implement this endpoint)
        async function listInputFiles() {
            const fileDiv = document.getElementById('files');
            fileDiv.innerHTML = '<em>Note: File listing would require additional endpoint. For now, make sure your files exist in the input/ directory:</em><br>• fabric_swatch.png<br>• scene_destination.png<br>• curtain_mask.png';
        }

        // Handle curtain fabric form submission
        document.getElementById('inpaintForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = {
                fabric_swatch: document.getElementById('fabricSwatch').value,
                scene_destination: document.getElementById('sceneDestination').value,
                curtain_mask: document.getElementById('curtainMask').value,
                comfy_server: document.getElementById('comfyServer').value || null
            };

            try {
                document.getElementById('submitBtn').disabled = true;
                document.getElementById('submitBtn').textContent = '⏳ Starting...';

                const response = await fetch('/api/inpaint/run', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || 'Request failed');
                }

                const result = await response.json();
                currentPromptId = result.prompt_id;

                showResult('result', `✅ Workflow started!<br>Prompt ID: ${result.prompt_id}<br>Progress WebSocket: ${result.progress_ws}<br>Result URL: ${result.result_url}`, 'success');

                // Start progress monitoring
                startProgressMonitoring(result.prompt_id);

            } catch (error) {
                showResult('result', `❌ Error: ${error.message}`, 'error');
            } finally {
                document.getElementById('submitBtn').disabled = false;
                document.getElementById('submitBtn').textContent = '🎨 Start Curtain Generation';
            }
        });

        // Tab switching function
        function showTab(tabName) {
            // Hide all tab contents
            document.getElementById('filenameForm').style.display = 'none';
            document.getElementById('uploadForm').style.display = 'none';

            // Remove active class from all tab buttons
            document.getElementById('filenameTab').classList.remove('active');
            document.getElementById('uploadTab').classList.remove('active');

            // Show selected tab content and mark button as active
            if (tabName === 'filename') {
                document.getElementById('filenameForm').style.display = 'block';
                document.getElementById('filenameTab').classList.add('active');
            } else if (tabName === 'upload') {
                document.getElementById('uploadForm').style.display = 'block';
                document.getElementById('uploadTab').classList.add('active');
            }
        }

        // Handle upload form submission
        document.getElementById('uploadInpaintForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData();
            formData.append('fabric_swatch', document.getElementById('fabricSwatchFile').files[0]);
            formData.append('scene_destination', document.getElementById('sceneDestinationFile').files[0]);
            formData.append('curtain_mask', document.getElementById('curtainMaskFile').files[0]);

            const comfyServer = document.getElementById('comfyServerUpload').value;
            if (comfyServer) {
                formData.append('comfy_server', comfyServer);
            }

            try {
                document.getElementById('uploadSubmitBtn').disabled = true;
                document.getElementById('uploadSubmitBtn').textContent = '⏳ Uploading & Starting...';

                const response = await fetch('/api/inpaint/upload-and-run', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || 'Upload failed');
                }

                const result = await response.json();
                currentPromptId = result.prompt_id;

                showResult('result', `✅ Files uploaded and workflow started!<br>Prompt ID: ${result.prompt_id}<br>Progress WebSocket: ${result.progress_ws}<br>Result URL: ${result.result_url}<br>Session: ${result.session_id}`, 'success');

                // Start progress monitoring
                startProgressMonitoring(result.prompt_id);

            } catch (error) {
                showResult('result', `❌ Error: ${error.message}`, 'error');
            } finally {
                document.getElementById('uploadSubmitBtn').disabled = false;
                document.getElementById('uploadSubmitBtn').textContent = '📤 Upload & Generate';
            }
        });

        // Start progress monitoring via WebSocket
        function startProgressMonitoring(promptId) {
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressLog = document.getElementById('progressLog');

            progressContainer.style.display = 'block';
            progressLog.textContent = 'Connecting to progress stream...\n';

            // Close existing connection
            if (progressSocket) {
                progressSocket.close();
            }

            const wsUrl = `ws://${window.location.host}/ws/progress/${promptId}`;
            progressSocket = new WebSocket(wsUrl);

            progressSocket.onopen = () => {
                progressLog.textContent += '✅ Connected to progress stream\n';
            };

            progressSocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                const timestamp = new Date().toLocaleTimeString();

                progressLog.textContent += `[${timestamp}] ${JSON.stringify(data)}\n`;
                progressLog.scrollTop = progressLog.scrollHeight;

                // Update progress bar for progress events
                if (data.type === 'progress') {
                    const percent = Math.round((data.data.value / data.data.max) * 100);
                    progressFill.style.width = `${percent}%`;
                    progressFill.textContent = `${percent}%`;
                }

                // Handle completion
                if (data.type === 'completed' || (data.type === 'executing' && data.data && data.data.node === null)) {
                    progressLog.textContent += '🎉 Workflow completed! You can now download the result.\n';

                    // Add download button
                    setTimeout(() => {
                        const downloadBtn = document.createElement('button');
                        downloadBtn.textContent = '📥 Download Result';
                        downloadBtn.onclick = () => downloadResult(promptId);
                        downloadBtn.style.marginTop = '10px';
                        progressContainer.appendChild(downloadBtn);
                    }, 1000);
                }
            };

            progressSocket.onerror = (error) => {
                progressLog.textContent += `❌ WebSocket error: ${error}\n`;
            };

            progressSocket.onclose = () => {
                progressLog.textContent += '🔌 Progress stream disconnected\n';
            };
        }

        // Download result image
        function downloadResult(promptId) {
            const url = `/api/inpaint/result/${promptId}`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `result_${promptId}.png`;
            link.click();
        }

        // Helper function to show results
        function showResult(elementId, content, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            listInputFiles();
        });
    </script>
</body>

</html>