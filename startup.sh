#!/bin/bash
# Optimized startup script - Fast environment setup then service start

set -e

echo "🚀 Starting ComfyUI + FastAPI services..."
echo "======================================="

# Create log directories
mkdir -p /workspace/logs

# Step 1: Setup environment (downloads models/nodes on first run)
echo "🔧 Setting up environment (first run may take 10-15 minutes)..."
if ! /workspace/setup-environment.sh; then
    echo "⚠️  Environment setup had issues, but continuing..."
fi

# Function to check if ComfyUI is ready
wait_for_comfyui() {
    echo "⏳ Waiting for ComfyUI to start..."
    local max_attempts=60  # 5 minutes with 5-second intervals
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8188/system_stats >/dev/null 2>&1; then
            echo "✅ ComfyUI is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts: ComfyUI not ready yet..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "❌ ComfyUI failed to start within 5 minutes"
    echo "📋 ComfyUI logs:"
    tail -50 /workspace/logs/comfyui.log 2>/dev/null || echo "No logs available"
    return 1
}

# Step 2: Start ComfyUI
echo ""
echo "🎨 Starting ComfyUI..."
cd /workspace/ComfyUI

# Start ComfyUI with proper configuration
python main.py \
    --listen 0.0.0.0 \
    --port 8188 \
    --enable-cors-header \
    --cuda-device 0 \
    > /workspace/logs/comfyui.log 2>&1 &

COMFYUI_PID=$!
echo "   ComfyUI started with PID: $COMFYUI_PID"

# Wait for ComfyUI to be ready
if ! wait_for_comfyui; then
    echo "❌ Failed to start ComfyUI, checking logs..."
    tail -100 /workspace/logs/comfyui.log 2>/dev/null || echo "No ComfyUI logs available"
    kill $COMFYUI_PID 2>/dev/null || true
    exit 1
fi

# Show ComfyUI status
echo ""
echo "📊 ComfyUI Status:"
echo "=================="
echo "   • Server: http://localhost:8188"
echo "   • PID: $COMFYUI_PID"
echo "   • Logs: /workspace/logs/comfyui.log"

# Test ComfyUI API
if curl -s http://localhost:8188/system_stats | grep -q "system"; then
    echo "   • API: ✅ Responding"
else
    echo "   • API: ⚠️  May have issues"
fi

# Show custom nodes loaded
node_count=$(find /workspace/ComfyUI/custom_nodes -maxdepth 1 -type d | wc -l)
echo "   • Custom nodes: $((node_count - 1)) directories"

# Step 3: Start FastAPI
echo ""
echo "🚀 Starting FastAPI backend..."
cd /workspace

# Set environment variables
export COMFY_SERVER="127.0.0.1:8188"
export PYTHONPATH="/workspace:$PYTHONPATH"

echo "   • Server: http://localhost:8000"
echo "   • API Docs: http://localhost:8000/docs"
echo "   • Environment: Production"

# Start FastAPI with uv
exec uv run fastapi run app.main:app --host 0.0.0.0 --port 8000 --no-reload