# Production Dockerfile for ComfyUI + FastAPI
FROM python:3.13-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    git curl wget unzip ffmpeg \
    libsm6 libxext6 libfontconfig1 libglib2.0-0 libgl1 libxrender1 \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip, setuptools, wheel
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Install uv (fast Python package manager)
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:$PATH"

# Verify uv installation
RUN uv --version

# Set workspace
WORKDIR /workspace

# Copy FastAPI backend files
COPY pyproject.toml README.md ./ 
COPY app/ ./app/
COPY static/ ./static/
COPY Subject_Destination_API.json ./

# Install FastAPI dependencies using uv
RUN uv sync --no-dev

# Copy configuration files
COPY comfy-requirements.txt custom-nodes-repos.txt model-downloads.txt ./ 

# Create setup-environment.sh script directly in container
RUN cat > /workspace/setup-environment.sh << 'SETUP_EOF' && chmod +x /workspace/setup-environment.sh
#!/bin/bash
set -e

SETUP_MARKER="/workspace/.setup_complete"
COMFYUI_DIR="/workspace/ComfyUI"

echo "🚀 Setting up ComfyUI environment..."

# Check if setup already completed
if [ -f "$SETUP_MARKER" ]; then
    echo "✅ Environment already set up, skipping..."
    exit 0
fi

# Step 1: Install PyTorch
echo "📦 Installing PyTorch 2.8.0+cu129 (this will be fast on RunPod)..."
pip install torch==2.8.0+cu129 torchvision --extra-index-url https://download.pytorch.org/whl/cu129

# Install comfy-cli
echo "📦 Installing comfy-cli..."
pip install comfy-cli

# Step 2: Clone ComfyUI
if [ ! -d "$COMFYUI_DIR/.git" ]; then
    echo "📦 Cloning ComfyUI..."
    rm -rf "$COMFYUI_DIR"
    git clone https://github.com/comfyanonymous/ComfyUI.git "$COMFYUI_DIR"
    echo "✅ ComfyUI cloned"
else
    echo "✅ ComfyUI already exists"
fi

cd "$COMFYUI_DIR"

# Step 3: Install ComfyUI requirements
echo "📦 Installing ComfyUI requirements..."
pip install -r requirements.txt

# Step 4: Create model directories
echo "📁 Creating model directories..."
mkdir -p models/clip models/diffusion_models models/vae models/clip_vision models/style_models

# Step 5: Download models with progress tracking
download_with_progress() {
    local url="$1"
    local dest="$2"
    local desc="$3"
    
    echo "📥 Downloading: $desc"
    echo "   URL: $url"
    echo "   Destination: $dest"
    
    mkdir -p "$(dirname "$dest")"
    
    if wget -O "$dest.tmp" "$url" --progress=bar:force:noscroll --timeout=60 --tries=3; then
        mv "$dest.tmp" "$dest"
        size=$(stat -c%s "$dest" 2>/dev/null || echo "unknown")
        echo "✅ Downloaded: $(basename "$dest") ($size bytes)"
        return 0
    else
        echo "❌ Failed to download: $desc"
        rm -f "$dest.tmp"
        return 1
    fi
}

echo "📥 Downloading FLUX models (this may take 5-10 minutes)..."

# Download models from model-downloads.txt
while IFS='|' read -r url dest desc; do
    if [[ "$url" =~ ^https:// ]] && [ ! -z "$dest" ] && [ ! -z "$desc" ]; then
        if [ ! -f "$dest" ] || [ ! -s "$dest" ]; then
            download_with_progress "$url" "$dest" "$desc" || echo "⚠️ Continuing..."
        else
            echo "✅ Already exists: $(basename "$dest")"
        fi
    fi
done < /workspace/model-downloads.txt

# Step 6: Install custom nodes
echo "📦 Installing custom nodes from repositories..."
cd custom_nodes

# Install from custom-nodes-repos.txt
while read -r repo; do
    if [[ "$repo" =~ ^https://github.com ]] && [ ! -z "$repo" ]; then
        repo_name=$(basename "$repo" .git)
        echo "📦 Installing: $repo_name"
        
        if [ ! -d "$repo_name" ]; then
            git clone "$repo" "$repo_name" || echo "⚠️ Failed to clone $repo_name"
        else
            echo "✅ Already exists: $repo_name"
        fi
        
        # Install requirements if they exist
        if [ -f "$repo_name/requirements.txt" ]; then
            echo "   📋 Installing requirements for $repo_name"
            pip install -r "$repo_name/requirements.txt" || echo "⚠️ Failed to install requirements for $repo_name"
        fi
    fi
done < /workspace/custom-nodes-repos.txt

# Step 7: Install ComfyUI-Manager
if [ ! -d "ComfyUI-Manager" ]; then
    echo "📦 Installing ComfyUI-Manager..."
    git clone https://github.com/ltdrdata/ComfyUI-Manager.git
    if [ -f "ComfyUI-Manager/requirements.txt" ]; then
        pip install -r ComfyUI-Manager/requirements.txt || echo "⚠️ Failed to install ComfyUI-Manager requirements"
    fi
fi

cd /workspace

# Mark setup as complete
touch "$SETUP_MARKER"

echo ""
echo "🎉 Environment setup completed successfully!"
echo "📊 Setup Summary:"
echo "=================="
echo "✅ ComfyUI: Installed"
echo "✅ Custom nodes: $(ls ComfyUI/custom_nodes 2>/dev/null | wc -l) installed"
echo "✅ Models: Downloaded to ComfyUI/models/"
SETUP_EOF

# Create startup.sh script directly in container
RUN cat > /workspace/startup.sh << 'STARTUP_EOF' && chmod +x /workspace/startup.sh
#!/bin/bash
set -e

echo "🚀 Starting ComfyUI + FastAPI services..."
echo "======================================="

# Create log directories
mkdir -p /workspace/logs

# Step 1: Setup environment (downloads models/nodes on first run)
echo "🔧 Setting up environment (first run may take 10-15 minutes)..."
if ! /workspace/setup-environment.sh; then
    echo "⚠️ Environment setup had issues, but continuing..."
fi

# Function to check if ComfyUI is ready
wait_for_comfyui() {
    echo "⏳ Waiting for ComfyUI to start..."
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8188/system_stats >/dev/null 2>&1; then
            echo "✅ ComfyUI is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts: ComfyUI not ready yet..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "❌ ComfyUI failed to start within 5 minutes"
    echo "📋 ComfyUI logs:"
    tail -50 /workspace/logs/comfyui.log 2>/dev/null || echo "No logs available"
    return 1
}

# Step 2: Start ComfyUI
echo ""
echo "🎨 Starting ComfyUI..."
cd /workspace/ComfyUI

# Start ComfyUI with proper configuration
python main.py \
    --listen 0.0.0.0 \
    --port 8188 \
    --enable-cors-header \
    --cuda-device 0 \
    > /workspace/logs/comfyui.log 2>&1 &

COMFYUI_PID=$!
echo "   ComfyUI started with PID: $COMFYUI_PID"

# Wait for ComfyUI to be ready
if ! wait_for_comfyui; then
    echo "❌ Failed to start ComfyUI, checking logs..."
    tail -100 /workspace/logs/comfyui.log 2>/dev/null || echo "No ComfyUI logs available"
    kill $COMFYUI_PID 2>/dev/null || true
    exit 1
fi

# Show ComfyUI status
echo ""
echo "📊 ComfyUI Status:"
echo "=================="
echo "   • Server: http://localhost:8188"
echo "   • PID: $COMFYUI_PID"
echo "   • Logs: /workspace/logs/comfyui.log"

# Test ComfyUI API
if curl -s http://localhost:8188/system_stats | grep -q "system"; then
    echo "   • API: ✅ Responding"
else
    echo "   • API: ⚠️ May have issues"
fi

# Step 3: Start FastAPI
echo ""
echo "🚀 Starting FastAPI backend..."
cd /workspace

# Set environment variables
export COMFY_SERVER="127.0.0.1:8188"
export PYTHONPATH="/workspace:$PYTHONPATH"

echo "   • Server: http://localhost:8000"
echo "   • API Docs: http://localhost:8000/docs"
echo "   • Environment: Production"

# Start FastAPI with uv
exec uv run fastapi run app.main:app --host 0.0.0.0 --port 8000 --no-reload
STARTUP_EOF

# Create directories
RUN mkdir -p input output logs

# Verify scripts were created correctly
RUN echo "🔍 Verifying scripts..." && \
    ls -la /workspace/*.sh && \
    echo "📄 Script headers:" && \
    head -3 /workspace/startup.sh && \
    head -3 /workspace/setup-environment.sh

# Create health check script
RUN echo '#!/bin/bash' > /workspace/healthcheck.sh && \
    echo 'curl -f http://localhost:8188/system_stats >/dev/null 2>&1 && \' >> /workspace/healthcheck.sh && \
    echo 'curl -f http://localhost:8000/api/health >/dev/null 2>&1' >> /workspace/healthcheck.sh && \
    chmod +x /workspace/healthcheck.sh

# Expose ports
EXPOSE 8000 8188

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=600s --retries=3 \
    CMD /workspace/healthcheck.sh

# Start the application
CMD ["bash", "/workspace/startup.sh"]
